"""
图片转换器
Image Converter
"""

import os
import logging
from pathlib import Path
from typing import Optional
import asyncio
from PIL import Image, ImageOps
import io

logger = logging.getLogger(__name__)

class ImageConverter:
    """图片转换器类"""
    
    def __init__(self):
        self.supported_formats = {
            'input': ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp', '.gif'],
            'output': ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp', 'gif', 'pdf']
        }
    
    async def convert(self, source_path: str, output_path: str, target_format: str, 
                     quality: int = 90, compress: bool = False) -> bool:
        """
        转换图片格式
        
        Args:
            source_path: 源文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            quality: 质量参数 (1-100)
            compress: 是否压缩
            
        Returns:
            bool: 转换是否成功
        """
        try:
            target_format = target_format.lower()
            logger.info(f"开始图片转换: {source_path} -> {output_path} ({target_format})")
            
            # 使用线程池执行图片转换（避免阻塞）
            return await asyncio.get_event_loop().run_in_executor(
                None, self._convert_sync, source_path, output_path, target_format, quality, compress
            )
                
        except Exception as e:
            logger.error(f"图片转换失败: {str(e)}")
            return False
    
    def _convert_sync(self, source_path: str, output_path: str, target_format: str, 
                     quality: int, compress: bool) -> bool:
        """同步图片转换"""
        try:
            # 打开图片
            with Image.open(source_path) as img:
                # 处理RGBA模式（透明度）
                if target_format in ['jpg', 'jpeg'] and img.mode in ['RGBA', 'LA']:
                    # JPEG不支持透明度，转换为RGB
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])  # 使用alpha通道作为mask
                    else:
                        background.paste(img)
                    img = background
                
                # 处理调色板模式
                if img.mode == 'P':
                    img = img.convert('RGB')
                
                # 压缩处理
                if compress:
                    img = self._compress_image(img, quality)
                
                # 特殊格式处理
                if target_format == 'pdf':
                    return self._convert_to_pdf(img, output_path, quality)
                else:
                    return self._save_image(img, output_path, target_format, quality)
                    
        except Exception as e:
            logger.error(f"同步图片转换失败: {str(e)}")
            return False
    
    def _compress_image(self, img: Image.Image, quality: int) -> Image.Image:
        """压缩图片"""
        try:
            # 根据质量调整图片尺寸
            if quality < 50:
                # 低质量：减小尺寸
                ratio = quality / 50.0
                new_size = (int(img.width * ratio), int(img.height * ratio))
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            elif quality < 80:
                # 中等质量：轻微减小尺寸
                ratio = 0.5 + (quality - 50) / 60.0
                new_size = (int(img.width * ratio), int(img.height * ratio))
                img = img.resize(new_size, Image.Resampling.LANCZOS)
            
            return img
            
        except Exception as e:
            logger.error(f"图片压缩失败: {str(e)}")
            return img
    
    def _save_image(self, img: Image.Image, output_path: str, target_format: str, quality: int) -> bool:
        """保存图片"""
        try:
            save_kwargs = {}
            
            # 格式特定参数
            if target_format in ['jpg', 'jpeg']:
                save_kwargs.update({
                    'format': 'JPEG',
                    'quality': quality,
                    'optimize': True
                })
            elif target_format == 'png':
                save_kwargs.update({
                    'format': 'PNG',
                    'optimize': True
                })
                if quality < 90:
                    # PNG压缩通过减少颜色数量
                    img = img.quantize(colors=256 if quality > 70 else 128)
            elif target_format == 'webp':
                save_kwargs.update({
                    'format': 'WebP',
                    'quality': quality,
                    'optimize': True
                })
            elif target_format == 'bmp':
                save_kwargs.update({
                    'format': 'BMP'
                })
            elif target_format == 'tiff':
                save_kwargs.update({
                    'format': 'TIFF',
                    'compression': 'tiff_lzw'  # LZW压缩
                })
            elif target_format == 'gif':
                save_kwargs.update({
                    'format': 'GIF',
                    'optimize': True
                })
                # GIF需要调色板模式
                if img.mode != 'P':
                    img = img.quantize(colors=256)
            
            # 保存图片
            img.save(output_path, **save_kwargs)
            return True
            
        except Exception as e:
            logger.error(f"保存图片失败: {str(e)}")
            return False
    
    def _convert_to_pdf(self, img: Image.Image, output_path: str, quality: int) -> bool:
        """将图片转换为PDF"""
        try:
            # 确保图片是RGB模式
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # 创建PDF
            img.save(output_path, 'PDF', quality=quality, optimize=True)
            return True
            
        except Exception as e:
            logger.error(f"图片转PDF失败: {str(e)}")
            return False
    
    async def batch_convert(self, source_paths: list, output_dir: str, target_format: str, 
                          quality: int = 90, compress: bool = False) -> dict:
        """
        批量转换图片
        
        Args:
            source_paths: 源文件路径列表
            output_dir: 输出目录
            target_format: 目标格式
            quality: 质量参数
            compress: 是否压缩
            
        Returns:
            dict: 转换结果统计
        """
        results = {
            'success_count': 0,
            'failed_count': 0,
            'failed_files': []
        }
        
        os.makedirs(output_dir, exist_ok=True)
        
        for source_path in source_paths:
            try:
                filename = Path(source_path).stem
                output_path = os.path.join(output_dir, f"{filename}.{target_format}")
                
                success = await self.convert(source_path, output_path, target_format, quality, compress)
                
                if success:
                    results['success_count'] += 1
                    logger.info(f"批量转换成功: {source_path}")
                else:
                    results['failed_count'] += 1
                    results['failed_files'].append(source_path)
                    logger.error(f"批量转换失败: {source_path}")
                    
            except Exception as e:
                results['failed_count'] += 1
                results['failed_files'].append(source_path)
                logger.error(f"批量转换异常: {source_path} - {str(e)}")
        
        return results
    
    def get_image_info(self, image_path: str) -> dict:
        """获取图片信息"""
        try:
            with Image.open(image_path) as img:
                return {
                    'format': img.format,
                    'mode': img.mode,
                    'size': img.size,
                    'width': img.width,
                    'height': img.height,
                    'has_transparency': img.mode in ['RGBA', 'LA'] or 'transparency' in img.info
                }
        except Exception as e:
            logger.error(f"获取图片信息失败: {str(e)}")
            return {}
    
    def resize_image(self, source_path: str, output_path: str, width: int, height: int, 
                    maintain_aspect: bool = True) -> bool:
        """
        调整图片尺寸
        
        Args:
            source_path: 源文件路径
            output_path: 输出文件路径
            width: 目标宽度
            height: 目标高度
            maintain_aspect: 是否保持宽高比
            
        Returns:
            bool: 是否成功
        """
        try:
            with Image.open(source_path) as img:
                if maintain_aspect:
                    img.thumbnail((width, height), Image.Resampling.LANCZOS)
                else:
                    img = img.resize((width, height), Image.Resampling.LANCZOS)
                
                img.save(output_path)
                return True
                
        except Exception as e:
            logger.error(f"调整图片尺寸失败: {str(e)}")
            return False
