"""
文档转换器
Document Converter
"""

import os
import logging
from pathlib import Path
from typing import Optional
import asyncio

# 文档处理库
from docx import Document
from docx2pdf import convert as docx_to_pdf
import openpyxl
from pptx import Presentation
import pandas as pd
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
import markdown
from bs4 import BeautifulSoup

logger = logging.getLogger(__name__)

class DocumentConverter:
    """文档转换器类"""
    
    def __init__(self):
        self.supported_formats = {
            'input': ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt', '.md', '.txt', '.html'],
            'output': ['pdf', 'html', 'txt', 'docx', 'xlsx', 'csv', 'md']
        }
    
    async def convert(self, source_path: str, output_path: str, target_format: str, 
                     quality: int = 90, compress: bool = False) -> bool:
        """
        转换文档格式
        
        Args:
            source_path: 源文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            quality: 质量参数
            compress: 是否压缩
            
        Returns:
            bool: 转换是否成功
        """
        try:
            source_ext = Path(source_path).suffix.lower()
            target_format = target_format.lower()
            
            logger.info(f"开始转换: {source_path} -> {output_path} ({target_format})")
            
            # Word文档转换
            if source_ext in ['.docx', '.doc']:
                return await self._convert_word(source_path, output_path, target_format)
            
            # Excel文档转换
            elif source_ext in ['.xlsx', '.xls']:
                return await self._convert_excel(source_path, output_path, target_format)
            
            # PowerPoint转换
            elif source_ext in ['.pptx', '.ppt']:
                return await self._convert_powerpoint(source_path, output_path, target_format)
            
            # Markdown转换
            elif source_ext == '.md':
                return await self._convert_markdown(source_path, output_path, target_format)
            
            # 文本文件转换
            elif source_ext == '.txt':
                return await self._convert_text(source_path, output_path, target_format)
            
            # HTML转换
            elif source_ext == '.html':
                return await self._convert_html(source_path, output_path, target_format)
            
            else:
                logger.error(f"不支持的源文件格式: {source_ext}")
                return False
                
        except Exception as e:
            logger.error(f"文档转换失败: {str(e)}")
            return False
    
    async def _convert_word(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换Word文档"""
        try:
            doc = Document(source_path)
            
            if target_format == 'txt':
                # Word转TXT
                text_content = []
                for paragraph in doc.paragraphs:
                    text_content.append(paragraph.text)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(text_content))
                    
            elif target_format == 'html':
                # Word转HTML
                text_content = []
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_content.append(f'<p>{paragraph.text}</p>')
                
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Converted Document</title>
                </head>
                <body>
                    {''.join(text_content)}
                </body>
                </html>
                """
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                    
            elif target_format == 'pdf':
                # Word转PDF（需要安装docx2pdf）
                try:
                    from docx2pdf import convert
                    await asyncio.get_event_loop().run_in_executor(
                        None, convert, source_path, output_path
                    )
                except ImportError:
                    # 如果docx2pdf不可用，使用替代方案
                    return await self._word_to_pdf_alternative(source_path, output_path)
                    
            else:
                logger.error(f"Word不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Word转换失败: {str(e)}")
            return False
    
    async def _convert_excel(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换Excel文档"""
        try:
            if target_format == 'csv':
                # Excel转CSV
                df = pd.read_excel(source_path)
                df.to_csv(output_path, index=False, encoding='utf-8-sig')
                
            elif target_format == 'html':
                # Excel转HTML
                df = pd.read_excel(source_path)
                html_content = df.to_html(index=False, escape=False)
                
                full_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Excel Data</title>
                    <style>
                        table {{ border-collapse: collapse; width: 100%; }}
                        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                        th {{ background-color: #f2f2f2; }}
                    </style>
                </head>
                <body>
                    {html_content}
                </body>
                </html>
                """
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                    
            elif target_format == 'txt':
                # Excel转TXT
                df = pd.read_excel(source_path)
                df.to_csv(output_path, sep='\t', index=False, encoding='utf-8')
                
            else:
                logger.error(f"Excel不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Excel转换失败: {str(e)}")
            return False
    
    async def _convert_powerpoint(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换PowerPoint文档"""
        try:
            prs = Presentation(source_path)
            
            if target_format == 'txt':
                # PowerPoint转TXT
                text_content = []
                for slide in prs.slides:
                    for shape in slide.shapes:
                        if hasattr(shape, "text"):
                            text_content.append(shape.text)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(text_content))
                    
            elif target_format == 'html':
                # PowerPoint转HTML
                slides_content = []
                for i, slide in enumerate(prs.slides):
                    slide_text = []
                    for shape in slide.shapes:
                        if hasattr(shape, "text") and shape.text.strip():
                            slide_text.append(f'<p>{shape.text}</p>')
                    
                    if slide_text:
                        slides_content.append(f'<div class="slide"><h2>幻灯片 {i+1}</h2>{"".join(slide_text)}</div>')
                
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>PowerPoint Content</title>
                    <style>
                        .slide {{ margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; }}
                        .slide h2 {{ color: #333; }}
                    </style>
                </head>
                <body>
                    {''.join(slides_content)}
                </body>
                </html>
                """
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                    
            else:
                logger.error(f"PowerPoint不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"PowerPoint转换失败: {str(e)}")
            return False
    
    async def _convert_markdown(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换Markdown文档"""
        try:
            with open(source_path, 'r', encoding='utf-8') as f:
                md_content = f.read()
            
            if target_format == 'html':
                # Markdown转HTML
                html_content = markdown.markdown(md_content)
                full_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Markdown Document</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }}
                        code {{ background-color: #f4f4f4; padding: 2px 4px; }}
                        pre {{ background-color: #f4f4f4; padding: 10px; overflow-x: auto; }}
                    </style>
                </head>
                <body>
                    {html_content}
                </body>
                </html>
                """
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(full_html)
                    
            elif target_format == 'txt':
                # Markdown转TXT（去除标记）
                html_content = markdown.markdown(md_content)
                soup = BeautifulSoup(html_content, 'html.parser')
                plain_text = soup.get_text()
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(plain_text)
                    
            else:
                logger.error(f"Markdown不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Markdown转换失败: {str(e)}")
            return False
    
    async def _convert_text(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换文本文件"""
        try:
            with open(source_path, 'r', encoding='utf-8') as f:
                text_content = f.read()
            
            if target_format == 'html':
                # TXT转HTML
                html_content = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>Text Document</title>
                    <style>
                        body {{ font-family: monospace; white-space: pre-wrap; padding: 20px; }}
                    </style>
                </head>
                <body>
                    {text_content}
                </body>
                </html>
                """
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                    
            elif target_format == 'md':
                # TXT转Markdown
                lines = text_content.split('\n')
                md_lines = []
                for line in lines:
                    if line.strip():
                        md_lines.append(line)
                    else:
                        md_lines.append('')
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(md_lines))
                    
            else:
                logger.error(f"文本文件不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"文本转换失败: {str(e)}")
            return False
    
    async def _convert_html(self, source_path: str, output_path: str, target_format: str) -> bool:
        """转换HTML文件"""
        try:
            with open(source_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            if target_format == 'txt':
                # HTML转TXT
                soup = BeautifulSoup(html_content, 'html.parser')
                plain_text = soup.get_text()
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(plain_text)
                    
            else:
                logger.error(f"HTML不支持转换为: {target_format}")
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"HTML转换失败: {str(e)}")
            return False
    
    async def _word_to_pdf_alternative(self, source_path: str, output_path: str) -> bool:
        """Word转PDF的替代方案"""
        try:
            # 使用ReportLab创建PDF
            doc = Document(source_path)
            story = []
            styles = getSampleStyleSheet()
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    p = Paragraph(paragraph.text, styles['Normal'])
                    story.append(p)
                    story.append(Spacer(1, 12))
            
            pdf_doc = SimpleDocTemplate(output_path, pagesize=A4)
            pdf_doc.build(story)
            
            return True
            
        except Exception as e:
            logger.error(f"Word转PDF替代方案失败: {str(e)}")
            return False
