@echo off
chcp 65001 >nul
echo Starting File Converter...
echo Installing Python dependencies...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Switch to project directory
cd /d "%~dp0"

REM Install backend dependencies
echo Installing backend dependencies...
cd backend
pip install -r requirements.txt
if errorlevel 1 (
    echo Failed to install Python dependencies
    pause
    exit /b 1
)

REM Start backend service
echo Starting backend server...
start "Backend Server" python main.py

REM Wait for backend to start
timeout /t 3 >nul

REM Switch to frontend directory
cd ..\frontend

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
)

REM Install frontend dependencies
echo Installing frontend dependencies...
npm install
if errorlevel 1 (
    echo Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Start frontend application
echo Starting frontend application...
npm start

pause
