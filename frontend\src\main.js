/**
 * Electron主进程
 * Main Process for Electron
 */

const { app, BrowserWindow, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const axios = require('axios');
const fs = require('fs');

// 后端服务配置
const BACKEND_HOST = '127.0.0.1';
const BACKEND_PORT = 8000;
const BACKEND_URL = `http://${BACKEND_HOST}:${BACKEND_PORT}`;

let mainWindow;
let backendProcess;

// 检查后端服务是否运行
async function checkBackendHealth() {
    try {
        const response = await axios.get(`${BACKEND_URL}/health`, { timeout: 3000 });
        return response.status === 200;
    } catch (error) {
        return false;
    }
}

// 启动后端服务
function startBackendService() {
    return new Promise((resolve, reject) => {
        const backendPath = path.join(__dirname, '../../backend');
        const pythonExecutable = process.platform === 'win32' ? 'python' : 'python3';
        
        console.log(`启动后端服务: ${pythonExecutable} main.py`);
        console.log(`后端路径: ${backendPath}`);
        
        backendProcess = spawn(pythonExecutable, ['main.py'], {
            cwd: backendPath,
            env: { ...process.env }
        });
        
        backendProcess.stdout.on('data', (data) => {
            console.log(`Backend stdout: ${data}`);
        });
        
        backendProcess.stderr.on('data', (data) => {
            console.error(`Backend stderr: ${data}`);
        });
        
        backendProcess.on('close', (code) => {
            console.log(`后端服务退出，代码: ${code}`);
        });
        
        backendProcess.on('error', (error) => {
            console.error(`后端服务启动失败: ${error.message}`);
            reject(error);
        });
        
        // 等待服务启动
        setTimeout(async () => {
            const isHealthy = await checkBackendHealth();
            if (isHealthy) {
                console.log('后端服务启动成功');
                resolve();
            } else {
                console.error('后端服务启动失败或无响应');
                reject(new Error('Backend service failed to start'));
            }
        }, 3000);
    });
}

// 创建主窗口
function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        show: false,
        icon: path.join(__dirname, '../build/icon.png'),
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false
        }
    });
    
    // 加载应用页面
    mainWindow.loadFile(path.join(__dirname, 'renderer/index.html'));
    
    // 窗口准备好后显示
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // 开发环境下打开开发者工具
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }
    });
    
    // 窗口关闭事件
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
    
    // 处理外部链接
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });
}

// 应用准备就绪
app.whenReady().then(async () => {
    try {
        // 检查后端服务是否已经运行
        const isBackendRunning = await checkBackendHealth();
        
        if (!isBackendRunning) {
            console.log('后端服务未运行，正在启动...');
            await startBackendService();
        } else {
            console.log('后端服务已运行');
        }
        
        // 创建主窗口
        createWindow();
        
    } catch (error) {
        console.error('应用启动失败:', error);
        
        // 显示错误对话框
        dialog.showErrorBox(
            '启动失败',
            `应用启动失败，请检查Python环境和依赖包是否正确安装。\n\n错误信息: ${error.message}`
        );
        
        app.quit();
    }
});

// 所有窗口关闭时退出应用（macOS除外）
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// macOS上点击dock图标时重新创建窗口
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// 应用退出前清理
app.on('before-quit', () => {
    if (backendProcess) {
        console.log('正在关闭后端服务...');
        backendProcess.kill();
    }
});

// IPC事件处理
ipcMain.handle('get-backend-url', () => {
    return 'http://127.0.0.1:8000';
});

ipcMain.handle('select-files', async () => {
    const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile', 'multiSelections'],
        filters: [
            { name: '所有支持的文件', extensions: ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'txt', 'md', 'html', 'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp', 'gif'] },
            { name: '文档文件', extensions: ['pdf', 'docx', 'doc', 'xlsx', 'xls', 'pptx', 'ppt', 'txt', 'md', 'html'] },
            { name: '图片文件', extensions: ['jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp', 'gif'] },
            { name: '所有文件', extensions: ['*'] }
        ]
    });
    
    return result;
});

ipcMain.handle('select-save-path', async (event, defaultName) => {
    const result = await dialog.showSaveDialog(mainWindow, {
        defaultPath: defaultName,
        filters: [
            { name: '所有文件', extensions: ['*'] }
        ]
    });
    
    return result;
});

ipcMain.handle('show-item-in-folder', async (event, filePath) => {
    shell.showItemInFolder(filePath);
});

ipcMain.handle('open-path', async (event, filePath) => {
    shell.openPath(filePath);
});

ipcMain.handle('get-app-version', () => {
    return app.getVersion();
});

// 设置单实例应用
const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
    app.quit();
} else {
    app.on('second-instance', () => {
        // 当运行第二个实例时，将焦点对准主窗口
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });
}
