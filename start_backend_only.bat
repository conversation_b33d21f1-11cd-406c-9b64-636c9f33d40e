@echo off
chcp 65001 >nul
echo === File Converter Backend Test ===

REM Switch to project directory
cd /d "%~dp0"

REM Install critical missing packages
echo Installing critical packages...
cd backend
pip install fastapi python-pptx aiofiles python-multipart

REM Test backend
echo Testing backend configuration...
cd ..
python quick_test.py

REM Start backend if ready
echo Starting backend server...
cd backend
python main.py

pause
