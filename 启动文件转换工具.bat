@echo off
chcp 65001 >nul
title 文件转换工具

echo.
echo ╔══════════════════════════════════════╗
echo ║          文件转换工具 v1.0           ║
echo ║        File Converter Tool           ║
echo ╚══════════════════════════════════════╝
echo.

REM 检查必要文件
if not exist "backend\dist\FileConverter-Backend.exe" (
    echo ❌ 错误: 找不到后端服务文件
    echo    请确保 backend\dist\FileConverter-Backend.exe 存在
    echo.
    pause
    exit /b 1
)

if not exist "frontend\dist\win-unpacked\文件转换工具.exe" (
    echo ❌ 错误: 找不到前端应用文件
    echo    请确保 frontend\dist\win-unpacked\文件转换工具.exe 存在
    echo.
    pause
    exit /b 1
)

echo 🚀 正在启动文件转换工具...
echo.

REM 启动后端服务
echo [1/2] 启动后端服务...
start /min "文件转换工具-后端" "backend\dist\FileConverter-Backend.exe"

REM 等待后端启动
echo [2/2] 等待服务就绪...
timeout /t 4 /nobreak >nul

REM 启动前端应用
echo ✅ 启动前端应用...
start "文件转换工具" "frontend\dist\win-unpacked\文件转换工具.exe"

echo.
echo ╔══════════════════════════════════════╗
echo ║            启动完成！                ║
echo ║                                      ║
echo ║  📱 前端应用: 桌面窗口已打开         ║
echo ║  🔧 后端服务: 在后台运行             ║
echo ║  🌐 服务地址: http://127.0.0.1:8000  ║
echo ║                                      ║
echo ║  💡 使用方法:                        ║
echo ║     1. 拖拽文件到应用窗口            ║
echo ║     2. 选择目标格式                  ║
echo ║     3. 点击转换                      ║
echo ║     4. 下载结果文件                  ║
echo ║                                      ║
echo ║  ⚠️  关闭应用时后端服务会自动停止     ║
echo ╚══════════════════════════════════════╝
echo.

REM 保持窗口打开，显示状态信息
echo 按任意键关闭此窗口...
pause >nul
