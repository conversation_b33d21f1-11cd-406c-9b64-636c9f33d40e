"""
文件转换工具后端服务
File Converter Backend Service
"""

import os
import uvicorn
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import aiofiles
import tempfile
import shutil
from pathlib import Path
from typing import List, Optional
import logging
from config import BACKEND_HOST, BACKEND_PORT, LOG_LEVEL, LOG_FORMAT, DOWNLOADS_DIR, UPLOAD_DIR

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="文件转换工具 API",
    description="支持多种文档格式转换的API服务",
    version="1.0.0"
)

# CORS中间件配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建必要的目录
os.makedirs("temp", exist_ok=True)
os.makedirs("uploads", exist_ok=True)
os.makedirs("downloads", exist_ok=True)

# 挂载静态文件
app.mount("/downloads", StaticFiles(directory="downloads"), name="downloads")

# 包含路由
from app.api.converter_api import router as converter_router
app.include_router(converter_router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("文件转换工具后端服务启动")
    # 清理临时文件
    from app.utils.file_utils import cleanup_temp_files
    cleanup_temp_files()

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("文件转换工具后端服务关闭")
    from app.utils.file_utils import cleanup_temp_files
    cleanup_temp_files()

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "文件转换工具API服务",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "file-converter-api"}

@app.post("/api/v1/upload")
async def upload_file(file: UploadFile = File(...)):
    """文件上传接口"""
    try:
        # 检查文件大小（最大100MB）
        if file.size and file.size > 100 * 1024 * 1024:
            raise HTTPException(status_code=413, detail="文件大小超过限制（最大100MB）")
        
        # 生成临时文件路径
        temp_dir = tempfile.mkdtemp(dir="temp")
        file_path = os.path.join(temp_dir, file.filename)
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        # 获取文件信息
        from app.utils.file_utils import get_file_info
        file_info = get_file_info(file_path)
        
        return {
            "success": True,
            "file_id": os.path.basename(temp_dir),
            "filename": file.filename,
            "file_info": file_info,
            "message": "文件上传成功"
        }
        
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=BACKEND_HOST,
        port=BACKEND_PORT,
        reload=True,
        log_level=LOG_LEVEL.lower()
    )
