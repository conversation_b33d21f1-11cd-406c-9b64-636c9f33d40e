"""
文件转换API路由
File Conversion API Routes
"""

import os
import logging
import tempfile
import asyncio
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Form, UploadFile, File
from fastapi.responses import FileResponse
from pathlib import Path

# 导入配置
from config import SUPPORTED_FORMATS

from ..converters.document_converter import DocumentConverter
from ..converters.image_converter import ImageConverter
from ..converters.pdf_converter import PDFConverter
from typing import Optional
from ..utils.file_utils import cleanup_temp_files

logger = logging.getLogger(__name__)
router = APIRouter()

# 初始化转换器
doc_converter = DocumentConverter()
img_converter = ImageConverter()
pdf_converter = PDFConverter()

@router.get("/formats")
async def get_supported_formats_api():
    """获取支持的文件格式"""
    return SUPPORTED_FORMATS

@router.post("/convert")
async def convert_file(
    file_id: str = Form(...),
    target_format: str = Form(...),
    quality: Optional[int] = Form(90),
    compress: Optional[bool] = Form(False)
):
    """
    文件转换接口
    
    Args:
        file_id: 文件ID（上传时返回的ID）
        target_format: 目标格式
        quality: 质量（1-100，适用于图片和PDF）
        compress: 是否压缩
    """
    try:
        # 查找源文件
        temp_dir = os.path.join("temp", file_id)
        if not os.path.exists(temp_dir):
            raise HTTPException(status_code=404, detail="文件不存在或已过期")
        
        # 获取源文件路径
        source_files = [f for f in os.listdir(temp_dir) if os.path.isfile(os.path.join(temp_dir, f))]
        if not source_files:
            raise HTTPException(status_code=404, detail="源文件不存在")
        
        source_path = os.path.join(temp_dir, source_files[0])
        source_ext = os.path.splitext(source_files[0])[1].lower()
        
        # 生成输出文件路径
        output_filename = f"{os.path.splitext(source_files[0])[0]}.{target_format.lower()}"
        output_path = os.path.join("downloads", f"{file_id}_{output_filename}")
        
        # 根据文件类型选择转换器
        success = False
        
        if source_ext in ['.docx', '.doc', '.xlsx', '.xls', '.pptx', '.ppt']:
            # 文档转换
            success = await doc_converter.convert(
                source_path, output_path, target_format, quality, compress
            )
        elif source_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']:
            # 图片转换
            success = await img_converter.convert(
                source_path, output_path, target_format, quality, compress
            )
        elif source_ext == '.pdf':
            # PDF转换
            success = await pdf_converter.convert(
                source_path, output_path, target_format, quality, compress
            )
        else:
            raise HTTPException(status_code=400, detail=f"不支持的源文件格式: {source_ext}")
        
        if not success:
            raise HTTPException(status_code=500, detail="文件转换失败")
        
        # 检查输出文件是否存在
        if not os.path.exists(output_path):
            raise HTTPException(status_code=500, detail="转换完成但输出文件不存在")
        
        return {
            "success": True,
            "download_url": f"/downloads/{os.path.basename(output_path)}",
            "filename": output_filename,
            "message": "转换成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件转换失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"转换失败: {str(e)}")

@router.get("/download/{filename}")
async def download_file(filename: str):
    """文件下载接口"""
    file_path = os.path.join("downloads", filename)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="文件不存在")
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='application/octet-stream'
    )

@router.delete("/cleanup")
async def cleanup_files():
    """清理临时文件"""
    try:
        cleanup_temp_files()
        return {"success": True, "message": "临时文件清理完成"}
    except Exception as e:
        logger.error(f"清理文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")

@router.get("/status/{file_id}")
async def get_conversion_status(file_id: str):
    """获取转换状态"""
    temp_dir = os.path.join("temp", file_id)
    
    if not os.path.exists(temp_dir):
        return {"status": "not_found", "message": "文件不存在"}
    
    # 检查是否有转换完成的文件
    download_files = [f for f in os.listdir("downloads") if f.startswith(file_id)]
    
    if download_files:
        return {
            "status": "completed",
            "download_url": f"/downloads/{download_files[0]}",
            "filename": download_files[0]
        }
    else:
        return {"status": "pending", "message": "转换中..."}
