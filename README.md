# 文件转换工具 (File Converter)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Node.js](https://img.shields.io/badge/Node.js-16+-green.svg)](https://nodejs.org/)
[![Electron](https://img.shields.io/badge/Electron-27+-purple.svg)](https://www.electronjs.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.100+-teal.svg)](https://fastapi.tiangolo.com/)

A modern, cross-platform file converter with a beautiful GUI interface and powerful API backend. Convert between 20+ file formats including documents, images, and PDFs.

## ✨ Features

- 🖱️ **Drag & Drop Interface** - Simply drag files to convert
- 📦 **Batch Processing** - Convert multiple files at once  
- 🎯 **20+ File Formats** - Documents, images, PDFs and more
- 🌍 **Cross-Platform** - Windows, macOS, Linux
- 🚀 **Fast & Efficient** - Local processing, no cloud dependency
- 🎨 **Modern UI** - Beautiful, responsive interface
- 🔌 **RESTful API** - Integrate with your applications
- 📱 **Offline First** - Works without internet connection

## 🔧 Supported Formats

### 📄 Documents
- **Word** (.docx) ↔ PDF, HTML, TXT, Markdown
- **Excel** (.xlsx) ↔ PDF, CSV, HTML  
- **PowerPoint** (.pptx) ↔ PDF, HTML, TXT

### 🖼️ Images
- **Formats**: JPG, PNG, BMP, GIF, TIFF, WebP
- **Convert**: Any format to any format
- **Export**: Images to PDF

### 📃 PDF Processing
- **Extract**: PDF → Text, HTML, Images
- **Convert**: PDF ↔ Word documents
- **Split**: Multi-page PDF processing

## 🚀 Quick Start

### Option 1: One-Click Launch (Recommended)

**Windows:**
```cmd
start.bat
```

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

### Option 2: Manual Setup

**Backend:**
```bash
cd backend
pip install -r requirements.txt
python main.py
```

**Frontend:**
```bash
cd frontend  
npm install
npm start
```

## 💻 Installation

### Prerequisites
- **Python 3.8+** - [Download](https://www.python.org/downloads/)
- **Node.js 16+** - [Download](https://nodejs.org/)
- **Git** - [Download](https://git-scm.com/)

### Clone & Install
```bash
git clone https://github.com/b-deng/converter.git
cd converter

# Install backend dependencies
cd backend
pip install -r requirements.txt

# Install frontend dependencies  
cd ../frontend
npm install
```

## 📦 Build Executables

### Windows EXE Files
```cmd
# Interactive build menu
build_exe.bat

# Or build directly
cd backend
pyinstaller --onefile main.py

cd ../frontend
npm run build:win
```

### Cross-Platform
```bash
# macOS
npm run build:mac

# Linux
npm run build:linux

# All platforms
npm run build:all
```

## 🔌 API Usage

### REST API Endpoints

**Convert File:**
```http
POST /api/convert
Content-Type: multipart/form-data

file: [binary]
to_format: "pdf"
```

**Get Supported Formats:**
```http
GET /api/formats
```

**API Documentation:**  
http://127.0.0.1:8000/docs (when backend is running)

### Example Usage
```python
import requests

# Convert DOCX to PDF
with open('document.docx', 'rb') as f:
    response = requests.post(
        'http://127.0.0.1:8000/api/convert',
        files={'file': f},
        data={'to_format': 'pdf'}
    )
    
with open('document.pdf', 'wb') as f:
    f.write(response.content)
```

## 🏗️ Tech Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **Uvicorn** - Lightning-fast ASGI server
- **PyPDF2** - PDF processing
- **python-docx** - Word document handling
- **openpyxl** - Excel file processing
- **Pillow** - Image manipulation

### Frontend  
- **Electron** - Cross-platform desktop apps
- **Vue.js 3** - Progressive JavaScript framework
- **Element Plus** - Vue 3 UI library
- **Axios** - HTTP client

## 📁 Project Structure

```
converter/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── converters/     # Format converters
│   │   └── utils/          # Utilities
│   ├── main.py             # FastAPI app
│   └── requirements.txt    # Python dependencies
├── frontend/               # Electron frontend
│   ├── src/
│   │   ├── main.js         # Electron main process
│   │   └── renderer/       # Vue.js renderer
│   └── package.json        # Node.js dependencies
├── build_exe.bat          # Windows build script
├── start.bat              # Windows launcher
├── start.sh               # Unix launcher
└── README.md              # This file
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- FastAPI for the amazing Python web framework
- Electron for cross-platform desktop capabilities
- Vue.js for the reactive frontend framework
- All the open-source libraries that make this project possible

## 📧 Contact

If you have any questions or suggestions, please feel free to reach out!

---

⭐ **Star this repo if you find it helpful!** ⭐
