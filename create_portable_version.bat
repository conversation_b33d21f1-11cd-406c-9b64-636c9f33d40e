@echo off
chcp 65001 >nul
echo ========================================
echo    创建便携版文件转换工具
echo ========================================
echo.

echo 检查构建文件...

if not exist "backend\dist\FileConverter-Backend.exe" (
    echo ❌ 后端exe不存在，请先构建
    pause
    exit /b 1
)

if not exist "frontend\dist\win-unpacked\文件转换工具.exe" (
    echo ❌ 前端exe不存在，请先构建
    pause
    exit /b 1
)

echo ✅ 构建文件检查通过
echo.

echo 创建便携版目录...
if exist "FileConverter-Portable" rmdir /s /q "FileConverter-Portable"
mkdir "FileConverter-Portable"
mkdir "FileConverter-Portable\backend"
mkdir "FileConverter-Portable\frontend"

echo 复制后端文件...
copy "backend\dist\FileConverter-Backend.exe" "FileConverter-Portable\backend\"
mkdir "FileConverter-Portable\backend\downloads"
mkdir "FileConverter-Portable\backend\temp"
mkdir "FileConverter-Portable\backend\uploads"

echo 复制前端文件...
xcopy "frontend\dist\win-unpacked\*" "FileConverter-Portable\frontend\" /E /I /Q

echo 创建启动脚本...
(
echo @echo off
echo chcp 65001 ^>nul
echo echo 启动文件转换工具...
echo echo.
echo echo 启动后端服务...
echo start "后端服务" "backend\FileConverter-Backend.exe"
echo timeout /t 3 ^>nul
echo echo 启动前端应用...
echo start "文件转换工具" "frontend\文件转换工具.exe"
echo echo 应用已启动！
echo pause
) > "FileConverter-Portable\启动文件转换工具.bat"

echo 创建说明文件...
(
echo # 文件转换工具 - 便携版
echo.
echo ## 使用方法
echo 1. 双击 "启动文件转换工具.bat"
echo 2. 等待应用启动
echo 3. 拖拽文件进行转换
echo.
echo ## 文件说明
echo - backend/FileConverter-Backend.exe: 后端服务
echo - frontend/文件转换工具.exe: 前端应用
echo - 启动文件转换工具.bat: 一键启动脚本
echo.
echo ## 支持格式
echo - 文档: Word, Excel, PowerPoint, PDF, TXT, HTML, Markdown
echo - 图片: JPG, PNG, BMP, GIF, TIFF, WebP
echo.
echo ## 注意事项
echo - 首次运行可能需要允许通过防火墙
echo - 确保8000端口未被占用
echo - 关闭应用时会自动停止后端服务
) > "FileConverter-Portable\README.md"

echo.
echo ========================================
echo ✅ 便携版创建完成！
echo.
echo 📁 输出目录: FileConverter-Portable\
echo 🚀 启动方式: 双击 "启动文件转换工具.bat"
echo.
echo 📦 包含文件:
dir "FileConverter-Portable" /B
echo.
echo 💡 可以将整个 FileConverter-Portable 文件夹
echo    复制到任何Windows电脑上使用
echo ========================================
pause
