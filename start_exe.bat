@echo off
chcp 65001 >nul
echo ========================================
echo    文件转换工具 - 一键启动脚本
echo ========================================
echo.

echo 检查构建文件...

REM 检查后端exe是否存在
if not exist "backend\dist\FileConverter-Backend.exe" (
    echo ❌ 后端exe不存在，请先运行构建
    echo 运行命令: build_exe.bat 选择选项2
    pause
    exit /b 1
)

REM 检查前端exe是否存在
if not exist "frontend\dist\win-unpacked\文件转换工具.exe" (
    echo ❌ 前端exe不存在，请先运行构建
    echo 运行命令: build_exe.bat 选择选项1
    pause
    exit /b 1
)

echo ✅ 所有exe文件已就绪
echo.

echo 启动后端服务...
start "文件转换工具-后端服务" "backend\dist\FileConverter-Backend.exe"

echo 等待后端服务启动...
timeout /t 3 >nul

echo 检查后端服务状态...
curl -s http://127.0.0.1:8000/health >nul 2>&1
if errorlevel 1 (
    echo ⚠️  后端服务可能未完全启动，继续启动前端...
) else (
    echo ✅ 后端服务启动成功
)

echo.
echo 启动前端应用...
start "文件转换工具-前端应用" "frontend\dist\win-unpacked\文件转换工具.exe"

echo.
echo ========================================
echo 🚀 应用启动完成！
echo.
echo 📋 运行状态:
echo   - 后端服务: http://127.0.0.1:8000
echo   - 前端应用: 桌面应用窗口
echo.
echo 💡 使用说明:
echo   1. 拖拽文件到应用窗口
echo   2. 选择目标转换格式
echo   3. 点击转换按钮
echo   4. 下载转换后的文件
echo.
echo ⚠️  注意事项:
echo   - 关闭应用时会自动停止后端服务
echo   - 如需手动停止，请关闭所有相关窗口
echo ========================================
echo.
pause
