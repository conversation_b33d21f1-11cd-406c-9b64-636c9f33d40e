@echo off
chcp 65001 >nul
echo === File Converter - Simple Start ===

REM Switch to project directory
cd /d "%~dp0"

REM Install core dependencies first
echo Installing core backend dependencies...
cd backend
pip install --upgrade pip
pip install fastapi uvicorn[standard] python-multipart aiofiles
pip install python-docx openpyxl python-pptx PyPDF2 Pillow

if errorlevel 1 (
    echo Some packages failed to install, but trying to continue...
)

REM Test the backend
echo Testing backend...
cd ..
python quick_test.py

REM Start backend server
echo Starting backend server...
cd backend
echo Backend will be available at: http://127.0.0.1:8000
echo API documentation at: http://127.0.0.1:8000/docs
echo.
echo Press Ctrl+C to stop the server
python main.py

pause
