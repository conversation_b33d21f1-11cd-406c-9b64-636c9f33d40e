{"name": "file-converter-frontend", "version": "1.0.0", "description": "跨平台文件转换工具前端", "main": "src/main.js", "scripts": {"dev": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "build:all": "electron-builder --win --mac --linux", "dist": "npm run build", "pack": "electron-builder --dir", "start": "electron .", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["file-converter", "electron", "cross-platform", "document-conversion"], "author": "File Converter Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"axios": "^1.6.0", "vue": "^3.3.8", "element-plus": "^2.4.2", "@element-plus/icons-vue": "^2.1.0", "vue-router": "^4.2.5", "pinia": "^2.1.7"}, "build": {"appId": "com.fileconverter.app", "productName": "文件转换工具", "directories": {"buildResources": "build", "output": "dist"}, "files": ["src/**/*", "node_modules/**/*", "!node_modules/.cache", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "build/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "文件转换工具"}}}