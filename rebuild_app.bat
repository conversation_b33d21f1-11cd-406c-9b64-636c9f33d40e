@echo off
chcp 65001 >nul
echo 🔧 重新构建文件转换工具...

echo.
echo 1️⃣ 关闭可能运行的应用实例...
taskkill /f /im "文件转换工具.exe" 2>nul
taskkill /f /im "FileConverter-Backend.exe" 2>nul

echo.
echo 2️⃣ 清理构建目录...
cd frontend
timeout /t 2 >nul
if exist dist (
    echo 删除旧的构建文件...
    cmd /c "rmdir /s /q dist" 2>nul
    if exist dist (
        echo 某些文件仍被占用，请手动删除 frontend\dist 目录后重新运行此脚本
        pause
        exit /b 1
    )
)

echo.
echo 3️⃣ 确保后端EXE存在...
if not exist "..\backend\dist\FileConverter-Backend.exe" (
    echo 后端EXE文件不存在，正在构建...
    cd ..\backend
    call build_backend.bat
    cd ..\frontend
)

echo.
echo 4️⃣ 重新构建前端应用...
powershell -ExecutionPolicy Bypass -Command "npm run build:win"

if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo ✅ 构建完成！
echo 📁 安装程序位置: frontend\dist\文件转换工具 Setup 1.0.0.exe
echo 📁 便携版位置: frontend\dist\win-unpacked\文件转换工具.exe
echo.
pause
