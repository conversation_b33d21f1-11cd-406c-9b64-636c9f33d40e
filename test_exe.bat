@echo off
chcp 65001 >nul
echo ========================================
echo    文件转换工具 - EXE测试脚本
echo ========================================
echo.

echo 选择测试方式:
echo 1. 🚀 测试后端独立exe
echo 2. 📦 测试前端应用exe
echo 3. 🔧 安装并测试完整应用
echo 4. 📊 查看构建结果
echo.
set /p choice=请选择 (1-4): 

if "%choice%"=="1" goto test_backend
if "%choice%"=="2" goto test_frontend
if "%choice%"=="3" goto install_app
if "%choice%"=="4" goto show_results
goto end

:test_backend
echo 测试后端独立exe...
echo.
if not exist "backend\dist\FileConverter-Backend.exe" (
    echo ❌ 后端exe不存在，请先构建
    goto end
)

echo ✅ 后端exe文件存在
echo 文件大小:
dir "backend\dist\FileConverter-Backend.exe" | findstr "FileConverter-Backend.exe"
echo.

echo 启动后端服务...
echo 注意：服务将在后台运行，按Ctrl+C停止
echo.
start "后端服务" "backend\dist\FileConverter-Backend.exe"

echo 等待服务启动...
timeout /t 5 >nul

echo 测试API连接...
curl -s http://127.0.0.1:8000/health
echo.
echo.

echo 如果看到 {"status":"healthy","service":"file-converter-api"}
echo 说明后端exe运行正常！
goto end

:test_frontend
echo 测试前端应用exe...
echo.
if not exist "frontend\dist\win-unpacked\文件转换工具.exe" (
    echo ❌ 前端exe不存在，请先构建
    goto end
)

echo ✅ 前端exe文件存在
echo 文件大小:
dir "frontend\dist\win-unpacked\文件转换工具.exe" | findstr "文件转换工具.exe"
echo.

echo 启动前端应用...
start "文件转换工具" "frontend\dist\win-unpacked\文件转换工具.exe"
echo 前端应用已启动！
goto end

:install_app
echo 安装并测试完整应用...
echo.
if not exist "frontend\dist\文件转换工具 Setup 1.0.0.exe" (
    echo ❌ 安装程序不存在，请先构建
    goto end
)

echo ✅ 安装程序文件存在
echo 文件大小:
dir "frontend\dist\文件转换工具 Setup 1.0.0.exe" | findstr "文件转换工具 Setup"
echo.

echo 启动安装程序...
start "安装程序" "frontend\dist\文件转换工具 Setup 1.0.0.exe"
echo 安装程序已启动！
echo 请按照安装向导完成安装，然后从开始菜单启动应用。
goto end

:show_results
echo ========================================
echo           构建结果总览
echo ========================================
echo.

echo 📁 后端构建结果:
if exist "backend\dist\FileConverter-Backend.exe" (
    echo ✅ FileConverter-Backend.exe
    dir "backend\dist\FileConverter-Backend.exe" | findstr "FileConverter-Backend.exe"
) else (
    echo ❌ FileConverter-Backend.exe 不存在
)
echo.

echo 📁 前端构建结果:
if exist "frontend\dist\win-unpacked\文件转换工具.exe" (
    echo ✅ 文件转换工具.exe (可执行文件)
    dir "frontend\dist\win-unpacked\文件转换工具.exe" | findstr "文件转换工具.exe"
) else (
    echo ❌ 文件转换工具.exe 不存在
)

if exist "frontend\dist\文件转换工具 Setup 1.0.0.exe" (
    echo ✅ 文件转换工具 Setup 1.0.0.exe (安装程序)
    dir "frontend\dist\文件转换工具 Setup 1.0.0.exe" | findstr "文件转换工具 Setup"
) else (
    echo ❌ 文件转换工具 Setup 1.0.0.exe 不存在
)
echo.

echo 📋 使用说明:
echo 1. 后端独立exe: 适合服务器部署，提供API服务
echo 2. 前端可执行文件: 需要手动启动后端服务
echo 3. 安装程序: 推荐使用，包含完整功能和自动启动
echo.

echo 💡 测试建议:
echo - 先测试后端exe确保API正常
echo - 再测试前端exe确保界面正常
echo - 最后使用安装程序进行完整测试
goto end

:end
echo.
pause
