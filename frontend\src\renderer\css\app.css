/* 应用全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

#app {
    max-width: 1200px;
    margin: 0 auto;
}

/* 头部样式 */
.app-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.app-header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.app-header h1 i {
    margin-right: 15px;
    color: #ffd700;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* 主要内容区域 */
.app-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 上传区域 */
.upload-section {
    flex: 1;
}

.upload-card {
    border-radius: 12px;
    overflow: hidden;
}

.upload-area {
    min-height: 300px;
    border: 2px dashed #dcdfe6;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    background: #fafafa;
    position: relative;
}

.upload-area.drag-over {
    border-color: #409eff;
    background: #ecf5ff;
}

.upload-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 250px;
    text-align: center;
}

.upload-empty i {
    font-size: 4rem;
    color: #c0c4cc;
    margin-bottom: 20px;
}

.upload-text {
    margin-bottom: 15px;
}

.upload-text p {
    margin: 8px 0;
    color: #606266;
}

.upload-tip {
    color: #909399;
    font-size: 0.9rem;
}

/* 文件列表 */
.file-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.file-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.file-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-info i {
    font-size: 2rem;
    margin-right: 15px;
    color: #409eff;
}

.file-details {
    display: flex;
    flex-direction: column;
}

.file-name {
    font-weight: 600;
    color: #303133;
    margin-bottom: 4px;
}

.file-meta {
    font-size: 0.85rem;
    color: #909399;
}

.file-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.add-more-files {
    text-align: center;
    padding: 20px;
    border: 1px dashed #dcdfe6;
    border-radius: 8px;
    background: #fafafa;
}

/* 选项区域 */
.options-section {
    margin: 20px 0;
}

.options-card {
    border-radius: 12px;
}

.options-card label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #303133;
}

/* 结果区域 */
.results-section {
    margin-top: 20px;
}

.results-card {
    border-radius: 12px;
}

.result-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.result-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    background: #f0f9ff;
    border-radius: 6px;
    border-left: 4px solid #67c23a;
}

.result-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.result-info i {
    font-size: 1.2rem;
    margin-right: 12px;
}

.result-details {
    display: flex;
    flex-direction: column;
}

.result-name {
    font-weight: 600;
    color: #303133;
    margin-bottom: 2px;
}

.result-meta {
    font-size: 0.85rem;
    color: #67c23a;
}

.result-actions {
    display: flex;
    gap: 8px;
}

/* 进度对话框 */
.progress-content {
    text-align: center;
    padding: 20px;
}

.progress-text {
    margin-top: 15px;
    color: #606266;
}

/* 文件图标 */
.file-icon-document:before { content: "📄"; }
.file-icon-image:before { content: "🖼️"; }
.file-icon-pdf:before { content: "📕"; }
.file-icon-excel:before { content: "📊"; }
.file-icon-word:before { content: "📝"; }
.file-icon-powerpoint:before { content: "📎"; }
.file-icon-text:before { content: "📃"; }
.file-icon-other:before { content: "📁"; }

/* 响应式设计 */
@media (max-width: 768px) {
    .app-header h1 {
        font-size: 2rem;
    }
    
    .file-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .file-actions {
        width: 100%;
        justify-content: flex-end;
    }
    
    .el-col {
        margin-bottom: 20px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-item {
    animation: fadeInUp 0.3s ease;
}

/* 拖拽时的效果 */
.upload-area.drag-over::after {
    content: '释放文件以上传';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.2rem;
    color: #409eff;
    font-weight: 600;
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 6px;
    border: 2px solid #409eff;
}

/* 卡片阴影效果 */
.el-card {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s ease;
}

.el-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* 按钮样式增强 */
.el-button {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.el-button:hover {
    transform: translateY(-1px);
}

/* 选择器样式 */
.el-select {
    border-radius: 6px;
}

/* 进度条样式 */
.el-progress {
    margin: 20px 0;
}

/* 对话框样式 */
.el-dialog {
    border-radius: 12px;
    overflow: hidden;
}

.el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.el-dialog__title {
    color: white;
    font-weight: 600;
}
