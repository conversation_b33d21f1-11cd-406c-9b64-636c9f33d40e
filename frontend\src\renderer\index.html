<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件转换工具</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <link rel="stylesheet" href="css/app.css">
</head>
<body>
    <div id="app">
        <!-- 主标题 -->
        <header class="app-header">
            <h1>
                <i class="el-icon-document"></i>
                文件转换工具
            </h1>
            <p class="subtitle">支持多种文档和图片格式的相互转换</p>
        </header>

        <!-- 主要内容区域 -->
        <main class="app-main">
            <!-- 文件上传区域 -->
            <div class="upload-section">
                <el-card class="upload-card" shadow="hover">
                    <div class="upload-area" 
                         @drop="handleDrop" 
                         @dragover.prevent 
                         @dragenter.prevent
                         :class="{ 'drag-over': isDragOver }"
                         @dragenter="isDragOver = true"
                         @dragleave="isDragOver = false">
                        
                        <div v-if="!uploadedFiles.length" class="upload-empty">
                            <i class="el-icon-upload"></i>
                            <div class="upload-text">
                                <p><strong>拖拽文件到此处</strong></p>
                                <p>或 <el-button type="primary" @click="selectFiles">选择文件</el-button></p>
                            </div>
                            <div class="upload-tip">
                                支持: Word, Excel, PowerPoint, PDF, 图片文件等
                            </div>
                        </div>

                        <!-- 文件列表 -->
                        <div v-else class="file-list">
                            <div v-for="(file, index) in uploadedFiles" 
                                 :key="index" 
                                 class="file-item">
                                <div class="file-info">
                                    <i :class="getFileIcon(file.file_info.extension)"></i>
                                    <div class="file-details">
                                        <div class="file-name">{{ file.filename }}</div>
                                        <div class="file-meta">
                                            {{ file.file_info.size_human }} • {{ file.file_info.category }}
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="file-actions">
                                    <!-- 格式选择 -->
                                    <el-select v-model="file.targetFormat" 
                                             placeholder="选择输出格式"
                                             size="small"
                                             style="width: 120px;">
                                        <el-option 
                                            v-for="format in getAvailableFormats(file.file_info.extension)"
                                            :key="format.ext"
                                            :label="format.name"
                                            :value="format.ext">
                                        </el-option>
                                    </el-select>
                                    
                                    <!-- 转换按钮 -->
                                    <el-button type="success" 
                                             size="small"
                                             :loading="file.converting"
                                             :disabled="!file.targetFormat"
                                             @click="convertFile(file)">
                                        <i class="el-icon-refresh"></i>
                                        转换
                                    </el-button>
                                    
                                    <!-- 删除按钮 -->
                                    <el-button type="danger" 
                                             size="small"
                                             @click="removeFile(index)">
                                        <i class="el-icon-delete"></i>
                                    </el-button>
                                </div>
                            </div>
                            
                            <!-- 添加更多文件 -->
                            <div class="add-more-files">
                                <el-button @click="selectFiles" icon="el-icon-plus">
                                    添加更多文件
                                </el-button>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 转换选项 -->
            <div class="options-section" v-if="uploadedFiles.length">
                <el-card class="options-card">
                    <template #header>
                        <span>转换选项</span>
                    </template>
                    
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <label>质量设置</label>
                            <el-slider v-model="conversionOptions.quality"
                                     :min="10"
                                     :max="100"
                                     show-input
                                     show-input-controls>
                            </el-slider>
                        </el-col>
                        <el-col :span="8">
                            <label>压缩选项</label>
                            <el-switch v-model="conversionOptions.compress"
                                     active-text="启用压缩"
                                     inactive-text="不压缩">
                            </el-switch>
                        </el-col>
                        <el-col :span="8">
                            <label>批量操作</label>
                            <div>
                                <el-button @click="setAllFormats" size="small">
                                    统一格式
                                </el-button>
                                <el-button @click="convertAll" 
                                         type="primary" 
                                         size="small"
                                         :loading="batchConverting">
                                    批量转换
                                </el-button>
                            </div>
                        </el-col>
                    </el-row>
                </el-card>
            </div>

            <!-- 转换结果 -->
            <div class="results-section" v-if="convertedFiles.length">
                <el-card class="results-card">
                    <template #header>
                        <span>转换结果</span>
                        <el-button style="float: right; padding: 3px 0" type="text" @click="clearResults">
                            清空结果
                        </el-button>
                    </template>
                    
                    <div class="result-list">
                        <div v-for="(result, index) in convertedFiles" 
                             :key="index" 
                             class="result-item">
                            <div class="result-info">
                                <i class="el-icon-success" style="color: #67c23a;"></i>
                                <div class="result-details">
                                    <div class="result-name">{{ result.filename }}</div>
                                    <div class="result-meta">转换成功</div>
                                </div>
                            </div>
                            
                            <div class="result-actions">
                                <el-button size="small" @click="downloadFile(result)">
                                    <i class="el-icon-download"></i>
                                    下载
                                </el-button>
                                <el-button size="small" @click="openFile(result)">
                                    <i class="el-icon-view"></i>
                                    打开
                                </el-button>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>
        </main>

        <!-- 格式设置对话框 -->
        <el-dialog v-model="formatDialogVisible" title="设置统一输出格式" width="400px">
            <el-form>
                <el-form-item label="输出格式:">
                    <el-select v-model="batchFormat" placeholder="选择格式" style="width: 100%;">
                        <el-option label="PDF" value="pdf"></el-option>
                        <el-option label="Word文档" value="docx"></el-option>
                        <el-option label="HTML网页" value="html"></el-option>
                        <el-option label="纯文本" value="txt"></el-option>
                        <el-option label="PNG图片" value="png"></el-option>
                        <el-option label="JPG图片" value="jpg"></el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            
            <template #footer>
                <el-button @click="formatDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="applyBatchFormat">确定</el-button>
            </template>
        </el-dialog>

        <!-- 进度对话框 -->
        <el-dialog v-model="progressDialogVisible" title="转换进度" width="400px" :close-on-click-modal="false">
            <div class="progress-content">
                <el-progress :percentage="progressPercentage" :status="progressStatus"></el-progress>
                <p class="progress-text">{{ progressText }}</p>
            </div>
        </el-dialog>
    </div>

    <!-- 引入依赖 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
