#!/bin/bash

echo "Starting File Converter..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "Python 3 is not installed"
    echo "Please install Python 3.8+ from https://python.org"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed"
    echo "Please install Node.js from https://nodejs.org"
    exit 1
fi

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "Installing Python dependencies..."
cd backend

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "Failed to install Python dependencies"
    exit 1
fi

# 启动后端服务
echo "Starting backend server..."
python main.py &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 切换到前端目录
cd ../frontend

echo "Installing frontend dependencies..."
npm install
if [ $? -ne 0 ]; then
    echo "Failed to install Node.js dependencies"
    kill $BACKEND_PID
    exit 1
fi

echo "Starting frontend application..."
npm start

# 清理：当前端退出时，杀死后端进程
kill $BACKEND_PID 2>/dev/null
