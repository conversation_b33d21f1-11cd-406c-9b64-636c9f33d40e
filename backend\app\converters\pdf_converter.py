"""
PDF转换器
PDF Converter
"""

import os
import logging
from pathlib import Path
from typing import Optional, List
import asyncio
from PIL import Image
import PyPDF2
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
import io
try:
    import fitz  # PyMuPDF
except ImportError:
    fitz = None

logger = logging.getLogger(__name__)

class PDFConverter:
    """PDF转换器类"""
    
    def __init__(self):
        self.supported_formats = {
            'input': ['.pdf'],
            'output': ['txt', 'html', 'jpg', 'png', 'docx']
        }
    
    async def convert(self, source_path: str, output_path: str, target_format: str, 
                     quality: int = 90, compress: bool = False) -> bool:
        """
        转换PDF格式
        
        Args:
            source_path: 源文件路径
            output_path: 输出文件路径
            target_format: 目标格式
            quality: 质量参数
            compress: 是否压缩
            
        Returns:
            bool: 转换是否成功
        """
        try:
            target_format = target_format.lower()
            logger.info(f"开始PDF转换: {source_path} -> {output_path} ({target_format})")
            
            # 使用线程池执行PDF转换（避免阻塞）
            return await asyncio.get_event_loop().run_in_executor(
                None, self._convert_sync, source_path, output_path, target_format, quality, compress
            )
                
        except Exception as e:
            logger.error(f"PDF转换失败: {str(e)}")
            return False
    
    def _convert_sync(self, source_path: str, output_path: str, target_format: str, 
                     quality: int, compress: bool) -> bool:
        """同步PDF转换"""
        try:
            if target_format == 'txt':
                return self._pdf_to_text(source_path, output_path)
            elif target_format == 'html':
                return self._pdf_to_html(source_path, output_path)
            elif target_format in ['jpg', 'png']:
                return self._pdf_to_image(source_path, output_path, target_format, quality)
            elif target_format == 'docx':
                return self._pdf_to_docx(source_path, output_path)
            else:
                logger.error(f"PDF不支持转换为: {target_format}")
                return False
                
        except Exception as e:
            logger.error(f"同步PDF转换失败: {str(e)}")
            return False
    
    def _pdf_to_text(self, source_path: str, output_path: str) -> bool:
        """PDF转文本"""
        try:
            text_content = []
            
            # 方法1: 使用PyPDF2
            try:
                with open(source_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        text = page.extract_text()
                        if text.strip():
                            text_content.append(f"=== 第 {page_num + 1} 页 ===\n")
                            text_content.append(text)
                            text_content.append("\n\n")
            except Exception as e:
                logger.warning(f"PyPDF2提取失败，尝试使用PyMuPDF: {str(e)}")
                
                # 方法2: 使用PyMuPDF (更好的文本提取)
                if fitz:
                    try:
                        doc = fitz.open(source_path)
                        for page_num in range(len(doc)):
                            page = doc.load_page(page_num)
                            text = page.get_text()
                            if text.strip():
                                text_content.append(f"=== 第 {page_num + 1} 页 ===\n")
                                text_content.append(text)
                                text_content.append("\n\n")
                        doc.close()
                    except Exception as e2:
                        logger.error(f"PyMuPDF也失败了: {str(e2)}")
                        return False
                else:
                    logger.error("PyMuPDF不可用，且PyPDF2提取失败")
                    return False
            
            # 保存文本
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(''.join(text_content))
            
            return True
            
        except Exception as e:
            logger.error(f"PDF转文本失败: {str(e)}")
            return False
    
    def _pdf_to_html(self, source_path: str, output_path: str) -> bool:
        """PDF转HTML"""
        try:
            html_content = []
            html_content.append("""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>PDF Content</title>
                <style>
                    body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
                    .page { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; }
                    .page-header { font-weight: bold; color: #333; margin-bottom: 15px; }
                    .page-content { white-space: pre-wrap; line-height: 1.6; }
                </style>
            </head>
            <body>
            """)
            
            try:
                # 使用PyMuPDF提取文本
                doc = fitz.open(source_path)
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    text = page.get_text()
                    if text.strip():
                        html_content.append(f'<div class="page">')
                        html_content.append(f'<div class="page-header">第 {page_num + 1} 页</div>')
                        html_content.append(f'<div class="page-content">{text}</div>')
                        html_content.append('</div>')
                doc.close()
            except Exception as e:
                logger.warning(f"PyMuPDF失败，使用PyPDF2: {str(e)}")
                
                # 备用方案：PyPDF2
                with open(source_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        text = page.extract_text()
                        if text.strip():
                            html_content.append(f'<div class="page">')
                            html_content.append(f'<div class="page-header">第 {page_num + 1} 页</div>')
                            html_content.append(f'<div class="page-content">{text}</div>')
                            html_content.append('</div>')
            
            html_content.append("</body></html>")
            
            # 保存HTML
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(''.join(html_content))
            
            return True
            
        except Exception as e:
            logger.error(f"PDF转HTML失败: {str(e)}")
            return False
    
    def _pdf_to_image(self, source_path: str, output_path: str, image_format: str, quality: int) -> bool:
        """PDF转图片"""
        if not fitz:
            logger.error("PyMuPDF不可用，无法将PDF转换为图片")
            return False
            
        try:
            # 使用PyMuPDF将PDF转换为图片
            doc = fitz.open(source_path)
            
            if len(doc) == 1:
                # 单页PDF，直接保存
                page = doc.load_page(0)
                
                # 设置缩放比例（质量）
                zoom_factor = quality / 50.0  # 质量50对应1.0倍，100对应2.0倍
                mat = fitz.Matrix(zoom_factor, zoom_factor)
                
                pix = page.get_pixmap(matrix=mat)
                
                if image_format == 'png':
                    pix.save(output_path)
                else:
                    # 转换为PIL Image以支持其他格式
                    img_data = pix.tobytes("ppm")
                    img = Image.open(io.BytesIO(img_data))
                    img.save(output_path, image_format.upper(), quality=quality)
                
                pix = None
            else:
                # 多页PDF，保存为多个图片文件
                base_path = Path(output_path)
                base_name = base_path.stem
                base_dir = base_path.parent
                
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    
                    zoom_factor = quality / 50.0
                    mat = fitz.Matrix(zoom_factor, zoom_factor)
                    pix = page.get_pixmap(matrix=mat)
                    
                    page_output_path = base_dir / f"{base_name}_page_{page_num + 1}.{image_format}"
                    
                    if image_format == 'png':
                        pix.save(str(page_output_path))
                    else:
                        img_data = pix.tobytes("ppm")
                        img = Image.open(io.BytesIO(img_data))
                        img.save(str(page_output_path), image_format.upper(), quality=quality)
                    
                    pix = None
            
            doc.close()
            return True
            
        except Exception as e:
            logger.error(f"PDF转图片失败: {str(e)}")
            return False
    
    def _pdf_to_docx(self, source_path: str, output_path: str) -> bool:
        """PDF转Word文档"""
        try:
            from docx import Document
            
            # 提取PDF文本
            text_content = []
            
            if fitz:
                try:
                    doc = fitz.open(source_path)
                    for page_num in range(len(doc)):
                        page = doc.load_page(page_num)
                        text = page.get_text()
                        if text.strip():
                            text_content.append(text)
                    doc.close()
                except Exception as e:
                    logger.warning(f"PyMuPDF失败，使用PyPDF2: {str(e)}")
            
            # 如果fitz不可用或失败，使用PyPDF2
            if not text_content:
                with open(source_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        text = page.extract_text()
                        if text.strip():
                            text_content.append(text)
            
            # 创建Word文档
            docx_doc = Document()
            docx_doc.add_heading('PDF转换文档', 0)
            
            for i, text in enumerate(text_content):
                if i > 0:
                    docx_doc.add_page_break()
                
                docx_doc.add_heading(f'第 {i + 1} 页', level=1)
                
                # 按段落分割文本
                paragraphs = text.split('\n\n')
                for paragraph in paragraphs:
                    if paragraph.strip():
                        docx_doc.add_paragraph(paragraph.strip())
            
            docx_doc.save(output_path)
            return True
            
        except Exception as e:
            logger.error(f"PDF转Word失败: {str(e)}")
            return False
    
    async def merge_pdfs(self, source_paths: List[str], output_path: str) -> bool:
        """
        合并多个PDF文件
        
        Args:
            source_paths: 源PDF文件路径列表
            output_path: 输出PDF路径
            
        Returns:
            bool: 是否成功
        """
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None, self._merge_pdfs_sync, source_paths, output_path
            )
        except Exception as e:
            logger.error(f"PDF合并失败: {str(e)}")
            return False
    
    def _merge_pdfs_sync(self, source_paths: List[str], output_path: str) -> bool:
        """同步合并PDF"""
        try:
            pdf_writer = PyPDF2.PdfWriter()
            
            for source_path in source_paths:
                with open(source_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        pdf_writer.add_page(page)
            
            with open(output_path, 'wb') as output_file:
                pdf_writer.write(output_file)
            
            return True
            
        except Exception as e:
            logger.error(f"同步PDF合并失败: {str(e)}")
            return False
    
    async def split_pdf(self, source_path: str, output_dir: str, pages_per_file: int = 1) -> bool:
        """
        拆分PDF文件
        
        Args:
            source_path: 源PDF路径
            output_dir: 输出目录
            pages_per_file: 每个文件的页数
            
        Returns:
            bool: 是否成功
        """
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None, self._split_pdf_sync, source_path, output_dir, pages_per_file
            )
        except Exception as e:
            logger.error(f"PDF拆分失败: {str(e)}")
            return False
    
    def _split_pdf_sync(self, source_path: str, output_dir: str, pages_per_file: int) -> bool:
        """同步拆分PDF"""
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            with open(source_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                total_pages = len(pdf_reader.pages)
                
                base_name = Path(source_path).stem
                
                for start_page in range(0, total_pages, pages_per_file):
                    pdf_writer = PyPDF2.PdfWriter()
                    
                    end_page = min(start_page + pages_per_file, total_pages)
                    
                    for page_num in range(start_page, end_page):
                        page = pdf_reader.pages[page_num]
                        pdf_writer.add_page(page)
                    
                    output_filename = f"{base_name}_part_{start_page//pages_per_file + 1}.pdf"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    with open(output_path, 'wb') as output_file:
                        pdf_writer.write(output_file)
            
            return True
            
        except Exception as e:
            logger.error(f"同步PDF拆分失败: {str(e)}")
            return False
    
    def get_pdf_info(self, pdf_path: str) -> dict:
        """获取PDF信息"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                info = {
                    'pages': len(pdf_reader.pages),
                    'title': pdf_reader.metadata.get('/Title', '') if pdf_reader.metadata else '',
                    'author': pdf_reader.metadata.get('/Author', '') if pdf_reader.metadata else '',
                    'subject': pdf_reader.metadata.get('/Subject', '') if pdf_reader.metadata else '',
                    'creator': pdf_reader.metadata.get('/Creator', '') if pdf_reader.metadata else '',
                    'producer': pdf_reader.metadata.get('/Producer', '') if pdf_reader.metadata else '',
                    'encrypted': pdf_reader.is_encrypted
                }
                
                return info
                
        except Exception as e:
            logger.error(f"获取PDF信息失败: {str(e)}")
            return {}
