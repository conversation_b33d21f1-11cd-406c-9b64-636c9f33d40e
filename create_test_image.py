#!/usr/bin/env python3
"""
创建测试图片
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    """创建一个简单的测试图片"""
    # 创建一个200x100的白色背景图片
    img = Image.new('RGB', (200, 100), color='white')
    draw = ImageDraw.Draw(img)
    
    # 绘制一些简单的图形
    draw.rectangle([10, 10, 190, 90], outline='blue', width=2)
    draw.ellipse([50, 30, 150, 70], fill='lightblue')
    
    # 添加文字
    try:
        # 尝试使用默认字体
        draw.text((60, 45), "TEST", fill='black')
    except:
        # 如果字体加载失败，仍然绘制文字
        draw.text((60, 45), "TEST", fill='black')
    
    # 保存为PNG格式
    img.save('test_image.png')
    print("测试图片已创建: test_image.png")

if __name__ == "__main__":
    create_test_image()
