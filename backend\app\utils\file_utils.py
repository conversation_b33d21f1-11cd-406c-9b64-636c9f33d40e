"""
文件工具函数
File Utilities
"""

import os
import shutil
import mimetypes
from pathlib import Path
try:
    import magic
except ImportError:
    magic = None
import tempfile
import time
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

def get_file_info(file_path: str) -> Dict:
    """
    获取文件信息
    
    Args:
        file_path: 文件路径
        
    Returns:
        dict: 文件信息
    """
    try:
        file_stat = os.stat(file_path)
        file_size = file_stat.st_size
        
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(file_path)
        
        # 尝试使用python-magic获取更准确的类型
        if magic:
            try:
                mime_type_magic = magic.from_file(file_path, mime=True)
                if mime_type_magic:
                    mime_type = mime_type_magic
            except Exception:
                pass  # 如果magic不可用，使用mimetypes的结果
        
        # 文件扩展名
        file_ext = Path(file_path).suffix.lower()
        
        # 文件类别
        file_category = get_file_category(file_ext, mime_type)
        
        return {
            'filename': os.path.basename(file_path),
            'size': file_size,
            'size_human': format_file_size(file_size),
            'extension': file_ext,
            'mime_type': mime_type,
            'category': file_category,
            'modified_time': file_stat.st_mtime,
            'created_time': file_stat.st_ctime
        }
        
    except Exception as e:
        logger.error(f"获取文件信息失败: {str(e)}")
        return {}

def get_file_category(file_ext: str, mime_type: str = None) -> str:
    """
    根据文件扩展名和MIME类型确定文件类别
    
    Args:
        file_ext: 文件扩展名
        mime_type: MIME类型
        
    Returns:
        str: 文件类别
    """
    file_ext = file_ext.lower()
    
    # 文档类型
    document_exts = ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', 
                    '.txt', '.rtf', '.odt', '.ods', '.odp']
    
    # 图片类型
    image_exts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', 
                 '.svg', '.ico']
    
    # 音频类型
    audio_exts = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma']
    
    # 视频类型
    video_exts = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm']
    
    # 压缩文件
    archive_exts = ['.zip', '.rar', '.7z', '.tar', '.gz', '.bz2']
    
    # 代码文件
    code_exts = ['.py', '.js', '.html', '.css', '.java', '.cpp', '.c', '.php', 
                '.rb', '.go', '.rs', '.swift']
    
    if file_ext in document_exts:
        return 'document'
    elif file_ext in image_exts:
        return 'image'
    elif file_ext in audio_exts:
        return 'audio'
    elif file_ext in video_exts:
        return 'video'
    elif file_ext in archive_exts:
        return 'archive'
    elif file_ext in code_exts:
        return 'code'
    elif mime_type:
        if mime_type.startswith('text/'):
            return 'text'
        elif mime_type.startswith('image/'):
            return 'image'
        elif mime_type.startswith('audio/'):
            return 'audio'
        elif mime_type.startswith('video/'):
            return 'video'
    
    return 'other'

def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小
    
    Args:
        size_bytes: 字节数
        
    Returns:
        str: 格式化后的大小
    """
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f} {size_names[i]}"

def cleanup_temp_files(max_age_hours: int = 24):
    """
    清理临时文件
    
    Args:
        max_age_hours: 最大文件年龄（小时）
    """
    try:
        temp_dirs = ['temp', 'downloads']
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        for temp_dir in temp_dirs:
            if not os.path.exists(temp_dir):
                continue
                
            for item in os.listdir(temp_dir):
                item_path = os.path.join(temp_dir, item)
                
                try:
                    # 获取文件/目录的修改时间
                    file_age = current_time - os.path.getmtime(item_path)
                    
                    if file_age > max_age_seconds:
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path)
                            logger.info(f"删除临时目录: {item_path}")
                        else:
                            os.remove(item_path)
                            logger.info(f"删除临时文件: {item_path}")
                            
                except Exception as e:
                    logger.warning(f"删除临时文件失败 {item_path}: {str(e)}")
                    
    except Exception as e:
        logger.error(f"清理临时文件失败: {str(e)}")

def get_supported_formats() -> Dict:
    """
    获取支持的文件格式
    
    Returns:
        dict: 支持的格式信息
    """
    return {
        'input_formats': {
            'document': [
                {'ext': 'docx', 'name': 'Word文档 (.docx)'},
                {'ext': 'doc', 'name': 'Word文档 (.doc)'},
                {'ext': 'xlsx', 'name': 'Excel表格 (.xlsx)'},
                {'ext': 'xls', 'name': 'Excel表格 (.xls)'},
                {'ext': 'pptx', 'name': 'PowerPoint演示文稿 (.pptx)'},
                {'ext': 'ppt', 'name': 'PowerPoint演示文稿 (.ppt)'},
                {'ext': 'pdf', 'name': 'PDF文档 (.pdf)'},
                {'ext': 'txt', 'name': '文本文件 (.txt)'},
                {'ext': 'md', 'name': 'Markdown文件 (.md)'},
                {'ext': 'html', 'name': 'HTML文件 (.html)'}
            ],
            'image': [
                {'ext': 'jpg', 'name': 'JPEG图片 (.jpg)'},
                {'ext': 'jpeg', 'name': 'JPEG图片 (.jpeg)'},
                {'ext': 'png', 'name': 'PNG图片 (.png)'},
                {'ext': 'bmp', 'name': 'BMP图片 (.bmp)'},
                {'ext': 'tiff', 'name': 'TIFF图片 (.tiff)'},
                {'ext': 'webp', 'name': 'WebP图片 (.webp)'},
                {'ext': 'gif', 'name': 'GIF图片 (.gif)'}
            ]
        },
        'output_formats': {
            'document': [
                {'ext': 'pdf', 'name': 'PDF文档'},
                {'ext': 'html', 'name': 'HTML网页'},
                {'ext': 'txt', 'name': '纯文本'},
                {'ext': 'docx', 'name': 'Word文档'},
                {'ext': 'xlsx', 'name': 'Excel表格'},
                {'ext': 'csv', 'name': 'CSV表格'},
                {'ext': 'md', 'name': 'Markdown'}
            ],
            'image': [
                {'ext': 'jpg', 'name': 'JPEG图片'},
                {'ext': 'png', 'name': 'PNG图片'},
                {'ext': 'bmp', 'name': 'BMP图片'},
                {'ext': 'tiff', 'name': 'TIFF图片'},
                {'ext': 'webp', 'name': 'WebP图片'},
                {'ext': 'gif', 'name': 'GIF图片'},
                {'ext': 'pdf', 'name': 'PDF文档'}
            ]
        },
        'conversion_matrix': {
            # Word文档
            'docx': ['pdf', 'html', 'txt'],
            'doc': ['pdf', 'html', 'txt'],
            
            # Excel表格
            'xlsx': ['csv', 'html', 'txt', 'pdf'],
            'xls': ['csv', 'html', 'txt', 'pdf'],
            
            # PowerPoint
            'pptx': ['pdf', 'html', 'txt'],
            'ppt': ['pdf', 'html', 'txt'],
            
            # PDF
            'pdf': ['txt', 'html', 'jpg', 'png', 'docx'],
            
            # 文本
            'txt': ['html', 'md', 'pdf'],
            'md': ['html', 'txt', 'pdf'],
            'html': ['txt', 'pdf'],
            
            # 图片
            'jpg': ['png', 'bmp', 'tiff', 'webp', 'gif', 'pdf'],
            'jpeg': ['png', 'bmp', 'tiff', 'webp', 'gif', 'pdf'],
            'png': ['jpg', 'bmp', 'tiff', 'webp', 'gif', 'pdf'],
            'bmp': ['jpg', 'png', 'tiff', 'webp', 'gif', 'pdf'],
            'tiff': ['jpg', 'png', 'bmp', 'webp', 'gif', 'pdf'],
            'webp': ['jpg', 'png', 'bmp', 'tiff', 'gif', 'pdf'],
            'gif': ['jpg', 'png', 'bmp', 'tiff', 'webp', 'pdf']
        }
    }

def is_conversion_supported(source_ext: str, target_ext: str) -> bool:
    """
    检查是否支持指定的转换
    
    Args:
        source_ext: 源文件扩展名
        target_ext: 目标文件扩展名
        
    Returns:
        bool: 是否支持转换
    """
    formats = get_supported_formats()
    conversion_matrix = formats.get('conversion_matrix', {})
    
    source_ext = source_ext.lower().lstrip('.')
    target_ext = target_ext.lower().lstrip('.')
    
    return target_ext in conversion_matrix.get(source_ext, [])

def validate_file_size(file_path: str, max_size_mb: int = 100) -> bool:
    """
    验证文件大小
    
    Args:
        file_path: 文件路径
        max_size_mb: 最大文件大小（MB）
        
    Returns:
        bool: 文件大小是否符合要求
    """
    try:
        file_size = os.path.getsize(file_path)
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    except Exception:
        return False

def create_temp_directory() -> str:
    """
    创建临时目录
    
    Returns:
        str: 临时目录路径
    """
    return tempfile.mkdtemp(dir="temp")

def safe_filename(filename: str) -> str:
    """
    生成安全的文件名
    
    Args:
        filename: 原始文件名
        
    Returns:
        str: 安全的文件名
    """
    import re
    
    # 移除或替换危险字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # 限制长度
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename
