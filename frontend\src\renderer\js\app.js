/**
 * 前端应用逻辑
 * Frontend Application Logic
 */

const { ipcRenderer } = require('electron');

// Vue应用
const { createApp, ref, reactive, computed, onMounted } = Vue;

createApp({
    setup() {
        // 响应式数据
        const uploadedFiles = ref([]);
        const convertedFiles = ref([]);
        const isDragOver = ref(false);
        const batchConverting = ref(false);
        const formatDialogVisible = ref(false);
        const progressDialogVisible = ref(false);
        const batchFormat = ref('');
        const progressPercentage = ref(0);
        const progressStatus = ref('');
        const progressText = ref('');
        
        // 转换选项
        const conversionOptions = reactive({
            quality: 90,
            compress: false
        });
        
        // 后端URL
        let backendUrl = '';
        
        // 支持的格式
        const supportedFormats = ref({});
        
        // 初始化
        onMounted(async () => {
            try {
                backendUrl = await ipcRenderer.invoke('get-backend-url');
                await loadSupportedFormats();
            } catch (error) {
                console.error('初始化失败:', error);
                ElMessage.error('应用初始化失败');
            }
        });
        
        // 加载支持的格式
        const loadSupportedFormats = async () => {
            try {
                const response = await axios.get(`${backendUrl}/api/v1/formats`);
                supportedFormats.value = response.data;
            } catch (error) {
                console.error('加载格式失败:', error);
            }
        };
        
        // 选择文件
        const selectFiles = async () => {
            try {
                const result = await ipcRenderer.invoke('select-files');
                if (!result.canceled && result.filePaths.length > 0) {
                    await uploadFiles(result.filePaths);
                }
            } catch (error) {
                console.error('选择文件失败:', error);
                ElMessage.error('选择文件失败');
            }
        };
        
        // 处理拖拽
        const handleDrop = async (event) => {
            event.preventDefault();
            isDragOver.value = false;
            
            const files = Array.from(event.dataTransfer.files);
            const filePaths = files.map(file => file.path);
            
            if (filePaths.length > 0) {
                await uploadFiles(filePaths);
            }
        };
        
        // 上传文件
        const uploadFiles = async (filePaths) => {
            const loadingInstance = ElLoading.service({
                lock: true,
                text: '正在上传文件...'
            });
            
            try {
                for (const filePath of filePaths) {
                    const formData = new FormData();
                    
                    // 读取文件
                    const fs = require('fs');
                    const fileBuffer = fs.readFileSync(filePath);
                    const fileName = require('path').basename(filePath);
                    
                    // 创建Blob对象
                    const blob = new Blob([fileBuffer]);
                    formData.append('file', blob, fileName);
                    
                    const response = await axios.post(`${backendUrl}/api/v1/upload`, formData, {
                        headers: {
                            'Content-Type': 'multipart/form-data'
                        }
                    });
                    
                    if (response.data.success) {
                        uploadedFiles.value.push({
                            ...response.data,
                            converting: false,
                            targetFormat: ''
                        });
                    }
                }
                
                ElMessage.success(`成功上传 ${filePaths.length} 个文件`);
                
            } catch (error) {
                console.error('上传失败:', error);
                ElMessage.error('文件上传失败');
            } finally {
                loadingInstance.close();
            }
        };
        
        // 获取文件图标
        const getFileIcon = (extension) => {
            const ext = extension.toLowerCase();
            const iconMap = {
                '.pdf': 'file-icon-pdf',
                '.doc': 'file-icon-word',
                '.docx': 'file-icon-word',
                '.xls': 'file-icon-excel',
                '.xlsx': 'file-icon-excel',
                '.ppt': 'file-icon-powerpoint',
                '.pptx': 'file-icon-powerpoint',
                '.txt': 'file-icon-text',
                '.md': 'file-icon-text',
                '.html': 'file-icon-text',
                '.jpg': 'file-icon-image',
                '.jpeg': 'file-icon-image',
                '.png': 'file-icon-image',
                '.bmp': 'file-icon-image',
                '.gif': 'file-icon-image',
                '.tiff': 'file-icon-image',
                '.webp': 'file-icon-image'
            };
            
            return iconMap[ext] || 'file-icon-other';
        };
        
        // 获取可用格式
        const getAvailableFormats = (extension) => {
            const ext = extension.toLowerCase().replace('.', '');
            const matrix = supportedFormats.value.conversion_matrix || {};
            const availableExts = matrix[ext] || [];
            
            const allFormats = [
                ...(supportedFormats.value.output_formats?.document || []),
                ...(supportedFormats.value.output_formats?.image || [])
            ];
            
            return allFormats.filter(format => availableExts.includes(format.ext));
        };
        
        // 转换单个文件
        const convertFile = async (file) => {
            if (!file.targetFormat) {
                ElMessage.warning('请选择输出格式');
                return;
            }
            
            file.converting = true;
            
            try {
                const formData = new FormData();
                formData.append('file_id', file.file_id);
                formData.append('target_format', file.targetFormat);
                formData.append('quality', conversionOptions.quality);
                formData.append('compress', conversionOptions.compress);
                
                const response = await axios.post(`${backendUrl}/api/v1/convert`, formData);
                
                if (response.data.success) {
                    convertedFiles.value.push({
                        filename: response.data.filename,
                        download_url: response.data.download_url,
                        original_file: file.filename
                    });
                    
                    ElMessage.success(`${file.filename} 转换成功`);
                } else {
                    ElMessage.error(`${file.filename} 转换失败`);
                }
                
            } catch (error) {
                console.error('转换失败:', error);
                ElMessage.error(`${file.filename} 转换失败: ${error.response?.data?.detail || error.message}`);
            } finally {
                file.converting = false;
            }
        };
        
        // 移除文件
        const removeFile = (index) => {
            uploadedFiles.value.splice(index, 1);
        };
        
        // 设置统一格式
        const setAllFormats = () => {
            formatDialogVisible.value = true;
        };
        
        // 应用批量格式
        const applyBatchFormat = () => {
            if (!batchFormat.value) {
                ElMessage.warning('请选择输出格式');
                return;
            }
            
            uploadedFiles.value.forEach(file => {
                const availableFormats = getAvailableFormats(file.file_info.extension);
                const targetFormat = availableFormats.find(f => f.ext === batchFormat.value);
                
                if (targetFormat) {
                    file.targetFormat = batchFormat.value;
                }
            });
            
            formatDialogVisible.value = false;
            ElMessage.success('已设置统一输出格式');
        };
        
        // 批量转换
        const convertAll = async () => {
            const filesToConvert = uploadedFiles.value.filter(file => file.targetFormat && !file.converting);
            
            if (filesToConvert.length === 0) {
                ElMessage.warning('没有可转换的文件，请先选择输出格式');
                return;
            }
            
            batchConverting.value = true;
            progressDialogVisible.value = true;
            progressPercentage.value = 0;
            progressStatus.value = '';
            progressText.value = '准备开始批量转换...';
            
            let successCount = 0;
            let failCount = 0;
            
            try {
                for (let i = 0; i < filesToConvert.length; i++) {
                    const file = filesToConvert[i];
                    
                    progressText.value = `正在转换: ${file.filename}`;
                    progressPercentage.value = Math.floor((i / filesToConvert.length) * 100);
                    
                    try {
                        file.converting = true;
                        
                        const formData = new FormData();
                        formData.append('file_id', file.file_id);
                        formData.append('target_format', file.targetFormat);
                        formData.append('quality', conversionOptions.quality);
                        formData.append('compress', conversionOptions.compress);
                        
                        const response = await axios.post(`${backendUrl}/api/v1/convert`, formData);
                        
                        if (response.data.success) {
                            convertedFiles.value.push({
                                filename: response.data.filename,
                                download_url: response.data.download_url,
                                original_file: file.filename
                            });
                            successCount++;
                        } else {
                            failCount++;
                        }
                        
                    } catch (error) {
                        console.error(`转换失败 ${file.filename}:`, error);
                        failCount++;
                    } finally {
                        file.converting = false;
                    }
                    
                    // 短暂延迟，避免过快的请求
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
                progressPercentage.value = 100;
                progressStatus.value = 'success';
                progressText.value = `批量转换完成！成功: ${successCount}, 失败: ${failCount}`;
                
                setTimeout(() => {
                    progressDialogVisible.value = false;
                }, 2000);
                
                if (successCount > 0) {
                    ElMessage.success(`批量转换完成！成功转换 ${successCount} 个文件`);
                }
                
                if (failCount > 0) {
                    ElMessage.warning(`有 ${failCount} 个文件转换失败`);
                }
                
            } catch (error) {
                console.error('批量转换失败:', error);
                ElMessage.error('批量转换失败');
                progressDialogVisible.value = false;
            } finally {
                batchConverting.value = false;
            }
        };
        
        // 下载文件
        const downloadFile = async (result) => {
            try {
                const response = await axios.get(`${backendUrl}${result.download_url}`, {
                    responseType: 'blob'
                });
                
                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', result.filename);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);
                
                ElMessage.success('文件下载成功');
                
            } catch (error) {
                console.error('下载失败:', error);
                ElMessage.error('文件下载失败');
            }
        };
        
        // 打开文件
        const openFile = async (result) => {
            try {
                // 先下载到临时位置
                const response = await axios.get(`${backendUrl}${result.download_url}`, {
                    responseType: 'blob'
                });
                
                const tempPath = require('path').join(require('os').tmpdir(), result.filename);
                const fs = require('fs');
                
                // 将blob转换为buffer并保存
                const arrayBuffer = await response.data.arrayBuffer();
                const buffer = Buffer.from(arrayBuffer);
                fs.writeFileSync(tempPath, buffer);
                
                // 使用系统默认程序打开
                await ipcRenderer.invoke('open-path', tempPath);
                
            } catch (error) {
                console.error('打开文件失败:', error);
                ElMessage.error('打开文件失败');
            }
        };
        
        // 清空结果
        const clearResults = () => {
            convertedFiles.value = [];
        };
        
        return {
            uploadedFiles,
            convertedFiles,
            isDragOver,
            batchConverting,
            formatDialogVisible,
            progressDialogVisible,
            batchFormat,
            progressPercentage,
            progressStatus,
            progressText,
            conversionOptions,
            selectFiles,
            handleDrop,
            getFileIcon,
            getAvailableFormats,
            convertFile,
            removeFile,
            setAllFormats,
            applyBatchFormat,
            convertAll,
            downloadFile,
            openFile,
            clearResults
        };
    }
}).use(ElementPlus).mount('#app');
