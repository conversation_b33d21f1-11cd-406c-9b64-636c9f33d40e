<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件转换测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
        }
        .file-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .file-info {
            flex: 1;
        }
        .file-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .result-item {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
    </style>
</head>
<body>
    <h1>文件转换工具测试</h1>
    
    <div class="upload-area" id="uploadArea">
        <p><strong>拖拽文件到此处或点击选择文件</strong></p>
        <input type="file" id="fileInput" multiple style="display: none;">
        <button class="btn-primary" onclick="document.getElementById('fileInput').click()">选择文件</button>
    </div>
    
    <div id="fileList"></div>
    <div id="results"></div>
    
    <script>
        const BACKEND_URL = 'http://127.0.0.1:8000';
        let uploadedFiles = [];
        let convertedFiles = [];
        let supportedFormats = {};
        
        // 初始化
        async function init() {
            try {
                const response = await fetch(`${BACKEND_URL}/api/v1/formats`);
                supportedFormats = await response.json();
                console.log('支持的格式:', supportedFormats);
            } catch (error) {
                console.error('加载格式失败:', error);
                alert('无法连接到后端服务，请确保后端服务正在运行');
            }
        }
        
        // 文件上传
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/v1/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (result.success) {
                    uploadedFiles.push({
                        ...result,
                        targetFormat: ''
                    });
                    updateFileList();
                } else {
                    alert('文件上传失败: ' + result.message);
                }
            } catch (error) {
                console.error('上传失败:', error);
                alert('文件上传失败');
            }
        }
        
        // 获取可用格式
        function getAvailableFormats(extension) {
            const ext = extension.toLowerCase().replace('.', '');
            const matrix = supportedFormats.conversion_matrix || {};
            const availableExts = matrix[ext] || [];
            
            const allFormats = [
                ...(supportedFormats.output_formats?.document || []),
                ...(supportedFormats.output_formats?.image || [])
            ];
            
            return allFormats.filter(format => availableExts.includes(format.ext));
        }
        
        // 更新文件列表
        function updateFileList() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';
            
            uploadedFiles.forEach((file, index) => {
                const availableFormats = getAvailableFormats(file.file_info.extension);
                
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div class="file-info">
                        <div><strong>${file.filename}</strong></div>
                        <div>${file.file_info.size_human} • ${file.file_info.category}</div>
                    </div>
                    <div class="file-actions">
                        <select id="format_${index}">
                            <option value="">选择输出格式</option>
                            ${availableFormats.map(format => 
                                `<option value="${format.ext}">${format.name}</option>`
                            ).join('')}
                        </select>
                        <button class="btn-success" onclick="convertFile(${index})">转换</button>
                        <button class="btn-danger" onclick="removeFile(${index})">删除</button>
                    </div>
                `;
                fileList.appendChild(fileItem);
            });
        }
        
        // 转换文件
        async function convertFile(index) {
            const file = uploadedFiles[index];
            const formatSelect = document.getElementById(`format_${index}`);
            const targetFormat = formatSelect.value;
            
            if (!targetFormat) {
                alert('请选择输出格式');
                return;
            }
            
            const formData = new FormData();
            formData.append('file_id', file.file_id);
            formData.append('target_format', targetFormat);
            formData.append('quality', 90);
            formData.append('compress', false);
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/v1/convert`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                if (result.success) {
                    convertedFiles.push({
                        filename: result.filename,
                        download_url: result.download_url,
                        original_file: file.filename
                    });
                    updateResults();
                    alert('转换成功！');
                } else {
                    alert('转换失败: ' + result.message);
                }
            } catch (error) {
                console.error('转换失败:', error);
                alert('转换失败');
            }
        }
        
        // 删除文件
        function removeFile(index) {
            uploadedFiles.splice(index, 1);
            updateFileList();
        }
        
        // 更新结果列表
        function updateResults() {
            const results = document.getElementById('results');
            
            if (convertedFiles.length === 0) {
                results.innerHTML = '';
                return;
            }
            
            results.innerHTML = '<h3>转换结果</h3>';
            
            convertedFiles.forEach(result => {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.innerHTML = `
                    <div>
                        <div><strong>${result.filename}</strong></div>
                        <div>从 ${result.original_file} 转换而来</div>
                    </div>
                    <div>
                        <button class="btn-primary" onclick="downloadFile('${result.download_url}', '${result.filename}')">下载</button>
                    </div>
                `;
                results.appendChild(resultItem);
            });
        }
        
        // 下载文件
        async function downloadFile(downloadUrl, filename) {
            try {
                const response = await fetch(`${BACKEND_URL}${downloadUrl}`);
                const blob = await response.blob();
                
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                a.remove();
                window.URL.revokeObjectURL(url);
            } catch (error) {
                console.error('下载失败:', error);
                alert('下载失败');
            }
        }
        
        // 事件监听
        document.getElementById('fileInput').addEventListener('change', function(e) {
            Array.from(e.target.files).forEach(uploadFile);
        });
        
        // 拖拽支持
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '#f0f0f0';
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '';
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.style.backgroundColor = '';
            Array.from(e.dataTransfer.files).forEach(uploadFile);
        });
        
        // 初始化应用
        init();
    </script>
</body>
</html>
