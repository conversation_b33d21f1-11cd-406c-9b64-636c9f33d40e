# 安装指南 / Installation Guide

## 系统要求 / System Requirements

- **Python 3.8+** (必需 / Required)
- **Node.js 16+** (必需 / Required)
- **Windows 10+** / **macOS 10.15+** / **Linux**

## 手动安装步骤 / Manual Installation Steps

### 1. 安装 Python Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. 安装 Node.js Dependencies

```bash
cd frontend
npm install
```

### 3. 启动应用 / Start Application

#### 方法1: 使用启动脚本 / Using Startup Scripts

**Windows:**
```cmd
start.bat
```

**Linux/macOS:**
```bash
chmod +x start.sh
./start.sh
```

#### 方法2: 手动启动 / Manual Startup

**启动后端 / Start Backend:**
```bash
cd backend
python main.py
```

**启动前端 / Start Frontend (新终端 / New Terminal):**
```bash
cd frontend
npm start
```

## 下载链接 / Download Links

### Python
- **官网**: https://python.org/downloads/
- **Windows**: https://python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe
- **安装时请勾选**: "Add Python to PATH"

### Node.js
- **官网**: https://nodejs.org/
- **Windows**: https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi
- **推荐版本**: LTS (Long Term Support)

## 故障排除 / Troubleshooting

### Python 相关问题 / Python Issues

1. **"python 不是内部或外部命令"**
   - 确保 Python 已安装并添加到 PATH
   - 尝试使用 `python3` 代替 `python`

2. **"No module named 'xxx'"**
   - 运行: `pip install -r requirements.txt`
   - 如果还是失败，尝试: `python -m pip install --upgrade pip`

### Node.js 相关问题 / Node.js Issues

1. **"node 不是内部或外部命令"**
   - 确保 Node.js 已安装并添加到 PATH
   - 重启命令行窗口

2. **"npm install 失败"**
   - 尝试: `npm cache clean --force`
   - 然后重新运行: `npm install`

### 端口冲突 / Port Conflicts

- **后端端口**: 8000 (如果被占用，修改 `backend/config.py`)
- **前端端口**: 自动分配 (通常是 3000 或其他可用端口)

## 验证安装 / Verify Installation

1. **测试后端**:
   ```bash
   python test_backend.py
   ```

2. **访问后端 API**:
   - 打开浏览器访问: http://127.0.0.1:8000
   - API 文档: http://127.0.0.1:8000/docs

3. **测试转换功能**:
   - 启动完整应用后，尝试上传和转换文件

## 支持的转换格式 / Supported Formats

### 文档 / Documents
- **Word**: .docx, .doc → PDF, TXT, HTML, MD
- **Excel**: .xlsx, .xls → PDF, CSV, HTML, TXT  
- **PowerPoint**: .pptx, .ppt → PDF, TXT, HTML

### 图片 / Images
- **格式**: JPG, PNG, BMP, GIF, TIFF, WebP
- **互转**: 支持所有格式之间转换
- **导出**: 可导出为 PDF

### PDF
- **转换为**: TXT, HTML, DOCX, 图片 (JPG/PNG)
- **高级功能**: 合并、拆分、文本提取
