# 📦 文件转换工具 - 打包指南

## 🎯 打包方案

### 1. 完整应用打包 (推荐)
**输出**: 包含前端界面和后端服务的完整安装程序
- ✅ 用户体验最佳，一键安装
- ✅ 自动启动后端服务
- ✅ 现代化 GUI 界面
- 📦 输出: `.exe` 安装程序 (~150MB)

### 2. 独立后端打包
**输出**: 仅包含后端 API 服务的可执行文件
- ✅ 适合服务器部署
- ✅ 可通过浏览器访问
- ✅ 体积更小
- 📦 输出: `.exe` 可执行文件 (~50MB)

## 🛠️ 环境准备

### 必需软件
```bash
# Python 3.8+
python --version

# Node.js 16+
node --version
npm --version
```

### 安装打包工具
```bash
# 运行自动安装脚本
build_exe.bat
# 选择 "3. 安装打包工具"
```

或手动安装：
```bash
# Python 后端打包工具
pip install pyinstaller

# 前端打包工具
cd frontend
npm install
npm install --save-dev electron-builder
```

## 🚀 快速打包

### 方法一：使用打包助手 (推荐)
```cmd
build_exe.bat
```
然后按提示选择打包方式。

### 方法二：手动打包

#### 完整应用打包
```bash
cd frontend
npm install
npm run build:win
```

#### 仅后端打包
```bash
cd backend
python build_backend.bat
```

## 📁 输出位置

### 完整应用
- 位置: `frontend/dist/`
- 文件: `文件转换工具 Setup 1.0.0.exe`
- 大小: ~150MB
- 安装后程序位置: `C:\Program Files\文件转换工具\`

### 独立后端
- 位置: `backend/dist/`
- 文件: `FileConverter-Backend.exe`
- 大小: ~50MB
- 使用: 直接运行，访问 http://127.0.0.1:8000

## 🔧 打包配置

### Electron 配置 (`frontend/package.json`)
```json
{
  "build": {
    "appId": "com.fileconverter.app",
    "productName": "文件转换工具",
    "win": {
      "target": "nsis",
      "icon": "build/icon.ico"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true
    }
  }
}
```

### PyInstaller 配置 (`backend/build_spec.py`)
```python
# 主要配置项
exe = EXE(
    name='FileConverter-Backend',
    console=True,  # 显示控制台
    icon='../frontend/build/icon.ico'
)
```

## 🎨 自定义图标

### 准备图标文件
1. **ICO 格式** (Windows): `frontend/build/icon.ico`
2. **ICNS 格式** (macOS): `frontend/build/icon.icns`  
3. **PNG 格式** (Linux): `frontend/build/icon.png`

### 图标规格要求
- **Windows ICO**: 16x16, 32x32, 48x48, 256x256
- **macOS ICNS**: 16x16 到 1024x1024 多种尺寸
- **Linux PNG**: 512x512 推荐

## 🐛 常见问题

### 1. PyInstaller 打包失败
```bash
# 解决方案：安装完整依赖
pip install --upgrade pip
pip install pyinstaller
pip install -r requirements.txt
```

### 2. Electron 打包卡住
```bash
# 解决方案：清理缓存
cd frontend
npm cache clean --force
rm -rf node_modules
npm install
```

### 3. 缺少依赖模块
编辑 `backend/build_spec.py`，在 `hiddenimports` 中添加：
```python
hiddenimports = [
    'your_missing_module',
    # ... 其他模块
]
```

### 4. 图标不显示
确保图标文件存在：
- `frontend/build/icon.ico` (Windows)
- 图标格式正确，大小合适

## 📊 打包性能优化

### 减小体积
1. **排除不必要的模块**:
   ```python
   excludes=[
       'tkinter', 'matplotlib', 'numpy', 'scipy'
   ]
   ```

2. **启用 UPX 压缩**:
   ```python
   upx=True
   ```

3. **仅包含必要文件**:
   ```json
   "files": [
       "src/**/*",
       "!node_modules/.cache"
   ]
   ```

### 加速打包
1. **使用本地缓存**
2. **并行构建** (如果支持)
3. **排除开发依赖**

## 🌍 跨平台打包

### 在 Windows 上打包
```bash
# Windows
npm run build:win

# macOS (需要在 macOS 系统上运行)
npm run build:mac

# Linux (需要在 Linux 系统上运行)  
npm run build:linux

# 所有平台 (需要相应系统)
npm run build:all
```

### 使用 GitHub Actions (CI/CD)
可以设置自动化打包，在推送代码时自动生成各平台安装包。

## 🔒 代码签名 (可选)

### Windows 代码签名
```json
{
  "win": {
    "certificateFile": "path/to/certificate.p12",
    "certificatePassword": "password"
  }
}
```

### macOS 代码签名
```json
{
  "mac": {
    "identity": "Developer ID Application: Your Name"
  }
}
```

## 📝 发布清单

- [ ] 测试打包环境
- [ ] 准备应用图标
- [ ] 更新版本号
- [ ] 运行完整打包
- [ ] 测试安装程序
- [ ] 验证所有功能
- [ ] 准备发布说明

## 💡 最佳实践

1. **版本管理**: 保持版本号同步
2. **测试充分**: 打包前完整测试
3. **文档更新**: 及时更新使用说明  
4. **备份重要**: 保留工作版本
5. **用户反馈**: 收集使用体验

---

🎉 **祝你打包成功！** 如有问题，请查看错误日志或联系技术支持。
