# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller 打包配置文件
File Converter Backend Packaging Configuration
"""

import os
import sys
from PyInstaller.building.build_main import Analysis, PYZ, EXE, COLLECT
from PyInstaller.building.api import BUNDLE

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(__file__))

# 需要包含的数据文件
datas = [
    # 配置文件
    ('config.py', '.'),
    # 模板文件（如果有）
    # ('templates/*', 'templates'),
]

# 需要包含的二进制文件
binaries = []

# 隐藏导入（避免打包时遗漏）
hiddenimports = [
    'fastapi',
    'uvicorn',
    'uvicorn.workers',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.lifespan.on',
    'pydantic',
    'starlette',
    'multipart',
    'python_multipart',
    'aiofiles',
    'PyPDF2',
    'docx',
    'openpyxl',
    'pptx',
    'PIL',
    'PIL.Image',
    'PIL.ImageFile',
    'logging.config',
]

# 分析主脚本
a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'scipy',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建EXE
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='FileConverter-Backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 显示控制台窗口，便于调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='../frontend/build/icon.ico' if os.path.exists('../frontend/build/icon.ico') else None,
)
