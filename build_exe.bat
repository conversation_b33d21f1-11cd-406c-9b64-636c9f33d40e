@echo off
chcp 65001 >nul
echo ========================================
echo    文件转换工具 - EXE打包助手
echo ========================================
echo.
echo 选择打包方式:
echo 1. 🚀 完整应用打包 (推荐)
echo 2. 🔧 仅后端打包 (Python)
echo 3. 📦 安装打包工具
echo 4. 🧪 测试打包环境
echo 5. 📖 查看打包说明
echo.
set /p choice=请选择 (1-5): 

if "%choice%"=="1" goto build_full
if "%choice%"=="2" goto build_backend
if "%choice%"=="3" goto install_tools
if "%choice%"=="4" goto test_env
if "%choice%"=="5" goto show_help
goto end

:install_tools
echo 安装打包工具...
echo 1. 安装 PyInstaller (Python后端打包)
pip install pyinstaller

echo 2. 安装 Node.js 依赖 (需要先安装Node.js)
where node >nul 2>&1
if errorlevel 1 (
    echo Node.js 未安装，请先安装 Node.js
    echo 下载地址: https://nodejs.org/
    pause
    goto end
)

cd frontend
call npm install
call npm install --save-dev electron-builder
echo 打包工具安装完成！
goto end

:build_backend
echo 打包Python后端...
cd backend
pyinstaller --onefile --windowed --name "FileConverter-Backend" main.py
echo 后端exe已生成在 backend/dist/ 目录
goto end

:build_full
echo 检查Node.js...
where node >nul 2>&1
if errorlevel 1 (
    echo 错误: 需要先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    goto end
)

echo 安装前端依赖...
cd frontend
call npm install

echo 安装打包工具...
call npm install --save-dev electron-builder

echo 开始打包完整应用...
call npm run build:win
echo 完整应用exe已生成在 frontend/dist/ 目录

:test_env
echo === 测试打包环境 ===
echo 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python 未安装
    goto end
)

echo 检查Node.js环境...
node --version
if errorlevel 1 (
    echo ❌ Node.js 未安装
    goto end
)

echo 检查依赖包...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo ❌ PyInstaller 未安装
) else (
    echo ✅ PyInstaller 已安装
)

cd frontend
npm list electron-builder >nul 2>&1
if errorlevel 1 (
    echo ❌ electron-builder 未安装
) else (
    echo ✅ electron-builder 已安装
)
cd ..

echo ✅ 环境检查完成
goto end

:show_help
echo ========================================
echo           打包说明文档
echo ========================================
echo.
echo 📋 支持的打包类型:
echo    1. Electron完整应用 (.exe安装程序)
echo    2. Python后端独立程序 (.exe可执行文件)
echo.
echo 🔧 环境要求:
echo    - Python 3.8+
echo    - Node.js 16+
echo    - PyInstaller (Python后端)
echo    - electron-builder (完整应用)
echo.
echo 📁 输出位置:
echo    - 完整应用: frontend/dist/
echo    - 后端程序: backend/dist/
echo.
echo 💡 使用建议:
echo    - 首次使用先选择"3"安装工具
echo    - 推荐使用"1"完整应用打包
echo    - 独立后端适合服务器部署
echo.
goto end

:end
pause
