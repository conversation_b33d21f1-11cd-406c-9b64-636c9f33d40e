@echo off
chcp 65001 >nul
echo === 打包 Python 后端 ===

REM 切换到后端目录
cd /d "%~dp0"

REM 检查PyInstaller是否安装
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 安装 PyInstaller...
    pip install pyinstaller
)

REM 清理之前的构建
if exist "build" rd /s /q "build"
if exist "dist" rd /s /q "dist"
if exist "*.spec" del "*.spec"

echo 开始打包后端...

REM 使用自定义规格文件打包
pyinstaller build_spec.py

REM 检查是否成功
if exist "dist\FileConverter-Backend.exe" (
    echo.
    echo ✅ 后端打包成功！
    echo 📁 exe文件位置: %cd%\dist\FileConverter-Backend.exe
    echo.
    echo 💡 使用方法:
    echo    1. 运行 FileConverter-Backend.exe
    echo    2. 后端将在 http://127.0.0.1:8000 启动
    echo    3. API文档: http://127.0.0.1:8000/docs
    echo.
) else (
    echo ❌ 打包失败，请检查错误信息
)

pause
