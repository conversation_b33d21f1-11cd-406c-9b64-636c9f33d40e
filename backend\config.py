"""
配置文件
Configuration
"""

import os
from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent

# 后端配置
BACKEND_HOST = "127.0.0.1"
BACKEND_PORT = 8000

# 文件上传配置
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
UPLOAD_DIR = PROJECT_ROOT / "uploads"
DOWNLOADS_DIR = PROJECT_ROOT / "downloads"

# 支持的文件格式
SUPPORTED_FORMATS = {
    "input_formats": {
        "document": [
            {"ext": "docx", "mime": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "name": "Word Document"},
            {"ext": "doc", "mime": "application/msword", "name": "Word 97-2003 Document"},
            {"ext": "xlsx", "mime": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "name": "Excel Workbook"},
            {"ext": "xls", "mime": "application/vnd.ms-excel", "name": "Excel 97-2003 Workbook"},
            {"ext": "pptx", "mime": "application/vnd.openxmlformats-officedocument.presentationml.presentation", "name": "PowerPoint Presentation"},
            {"ext": "ppt", "mime": "application/vnd.ms-powerpoint", "name": "PowerPoint 97-2003 Presentation"},
            {"ext": "txt", "mime": "text/plain", "name": "Text File"},
            {"ext": "md", "mime": "text/markdown", "name": "Markdown File"},
            {"ext": "html", "mime": "text/html", "name": "HTML File"}
        ],
        "image": [
            {"ext": "jpg", "mime": "image/jpeg", "name": "JPEG Image"},
            {"ext": "jpeg", "mime": "image/jpeg", "name": "JPEG Image"},
            {"ext": "png", "mime": "image/png", "name": "PNG Image"},
            {"ext": "bmp", "mime": "image/bmp", "name": "Bitmap Image"},
            {"ext": "gif", "mime": "image/gif", "name": "GIF Image"},
            {"ext": "tiff", "mime": "image/tiff", "name": "TIFF Image"},
            {"ext": "webp", "mime": "image/webp", "name": "WebP Image"}
        ],
        "pdf": [
            {"ext": "pdf", "mime": "application/pdf", "name": "PDF Document"}
        ]
    },
    "output_formats": {
        "document": [
            {"ext": "pdf", "name": "PDF Document"},
            {"ext": "docx", "name": "Word Document"},
            {"ext": "txt", "name": "Text File"},
            {"ext": "html", "name": "HTML File"},
            {"ext": "md", "name": "Markdown File"},
            {"ext": "csv", "name": "CSV File"}
        ],
        "image": [
            {"ext": "jpg", "name": "JPEG Image"},
            {"ext": "png", "name": "PNG Image"},
            {"ext": "bmp", "name": "Bitmap Image"},
            {"ext": "gif", "name": "GIF Image"},
            {"ext": "tiff", "name": "TIFF Image"},
            {"ext": "webp", "name": "WebP Image"},
            {"ext": "pdf", "name": "PDF (Image Container)"}
        ]
    },
    "conversion_matrix": {
        # 文档格式转换
        "docx": ["pdf", "txt", "html", "md"],
        "doc": ["pdf", "txt", "html", "md"],
        "xlsx": ["pdf", "csv", "html", "txt"],
        "xls": ["pdf", "csv", "html", "txt"],
        "pptx": ["pdf", "txt", "html"],
        "ppt": ["pdf", "txt", "html"],
        "txt": ["pdf", "html", "docx", "md"],
        "md": ["pdf", "html", "docx", "txt"],
        "html": ["pdf", "txt", "docx", "md"],
        
        # PDF转换
        "pdf": ["txt", "html", "docx", "jpg", "png"],
        
        # 图片格式转换
        "jpg": ["png", "bmp", "gif", "tiff", "webp", "pdf"],
        "jpeg": ["png", "bmp", "gif", "tiff", "webp", "pdf"],
        "png": ["jpg", "bmp", "gif", "tiff", "webp", "pdf"],
        "bmp": ["jpg", "png", "gif", "tiff", "webp", "pdf"],
        "gif": ["jpg", "png", "bmp", "tiff", "webp", "pdf"],
        "tiff": ["jpg", "png", "bmp", "gif", "webp", "pdf"],
        "webp": ["jpg", "png", "bmp", "gif", "tiff", "pdf"]
    }
}

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 转换选项默认值
DEFAULT_QUALITY = 90
DEFAULT_COMPRESS = False

# 临时文件清理时间（小时）
TEMP_FILE_CLEANUP_HOURS = 24

# 确保目录存在
UPLOAD_DIR.mkdir(exist_ok=True)
DOWNLOADS_DIR.mkdir(exist_ok=True)
