# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Backend specific
backend/build/
backend/dist/
backend/temp/
backend/uploads/
backend/downloads/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Frontend specific
frontend/dist/
frontend/build/
frontend/node_modules/
frontend/.cache/

# Electron
frontend/out/
frontend/app/dist/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.*.local

# Cache
.cache/
*.tmp
*.temp

# Temporary files
temp/
tmp/
*.bak
*.backup

# Test files
test_*.py
quick_test.py
test_build_env.py

# Package files
*.exe
*.msi
*.dmg
*.pkg
*.deb
*.rpm
*.AppImage

# Documentation build
docs/_build/
site/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/
